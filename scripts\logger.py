import logging
import sys
from datetime import datetime
from pathlib import Path

# Let's ensure the project root is on the path to import config
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config

def setup_logger():
    """
    Sets up a centralized logger for the project.
    
    Returns:
        logging.Logger: A configured logger instance.
    """
    # Create the logs directory using the path from our config file
    config.LOGS_DIR.mkdir(exist_ok=True)
    
    # Create a timestamped log file name
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file_name = f"economic_analyzer_{timestamp}.log"
    log_file_path = config.LOGS_DIR / log_file_name
    
    # Check if the logger has already been configured
    # This prevents adding duplicate handlers if setup_logger is called more than once
    logger = logging.getLogger("US_Economic_Analyzer")
    if logger.handlers:
        return logger # Return existing logger
        
    logger.setLevel(logging.INFO) # Set the minimum level of messages to handle
    
    # Define the format for log messages
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # Create a handler to write logs to the file
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # Create a handler to stream logs to the console
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    
    # Add both handlers to the logger
    logger.addHandler(file_handler)
    logger.addHandler(stream_handler)
    
    logger.info(f"Logger initialized. Logging to {log_file_path}")
    
    return logger

# Create a single logger instance to be imported by other modules
log = setup_logger()

if __name__ == '__main__':
    log.info("This is an info message.")
    log.warning("This is a warning message.")
    log.error("This is an error message.")
    log.debug("This message will NOT appear because the logger level is INFO.")