2025-07-07 16:02:32,352 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-07_16-02-32.log
2025-07-07 16:02:32,352 - INFO - [fetch_bea.py:22] - Starting BEA GDP by Industry data fetch for years: [2023, 2024, 2025]
2025-07-07 16:02:32,387 - INFO - [fetch_bea.py:38] - Found 30 unique BEA industry codes to filter for.
2025-07-07 16:02:32,387 - INFO - [fetch_bea.py:44] - Requesting data from BEA API...
2025-07-07 16:02:35,609 - INFO - [fetch_bea.py:53] - Successfully received data from BEA. Total rows returned: 891
2025-07-07 16:02:35,625 - INFO - [fetch_bea.py:63] - Filtered 891 rows down to 126 relevant industry rows.
2025-07-07 16:02:35,663 - INFO - [fetch_bea.py:102] - BEA data fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bea_gdp_by_industry_raw.csv
