2025-07-11 18:17:13,064 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\logs\economic_analyzer_2025-07-11_18-17-13.log
2025-07-11 18:17:13,064 - INFO - [process_and_score.py:688] - ===== Starting Data Processing and Scoring Pipeline =====
2025-07-11 18:17:13,064 - INFO - [process_and_score.py:66] - --- Starting Economic Value Add Score Calculation ---
2025-07-11 18:17:13,129 - INFO - [process_and_score.py:74] - Successfully loaded raw BEA GDP data and the master sheet.
2025-07-11 18:17:13,180 - INFO - [process_and_score.py:92] - Latest complete quarter available for all industries: 2025-Q1
2025-07-11 18:17:13,190 - INFO - [process_and_score.py:110] - Recent 4 Quarters for analysis: ['2025-Q1', '2024-Q4', '2024-Q3', '2024-Q2']
2025-07-11 18:17:13,190 - INFO - [process_and_score.py:111] - Previous 4 Quarters for analysis: ['2024-Q1', '2023-Q4', '2023-Q3', '2023-Q2']
2025-07-11 18:17:13,230 - INFO - [process_and_score.py:131] - Calculated and normalized GDP size and growth scores.
2025-07-11 18:17:14,222 - INFO - [process_and_score.py:167] - Economic Value Add Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\economic_value_add_score.xlsx
2025-07-11 18:17:14,222 - INFO - [process_and_score.py:185] - --- Starting Trade Imbalance Score Calculation ---
2025-07-11 18:17:14,288 - INFO - [process_and_score.py:191] - Successfully loaded raw DataWeb imports and exports data.
2025-07-11 18:17:14,288 - INFO - [process_and_score.py:197] - Aligning monthly trade data with latest GDP quarter: 2025-Q1
2025-07-11 18:17:14,312 - INFO - [process_and_score.py:208] - Analysis window covers 12 months ending on 2025-03-01.
2025-07-11 18:17:14,312 - INFO - [process_and_score.py:209] - First month in window: 2024-04-01, Last month in window: 2025-03-01.
2025-07-11 18:17:14,350 - INFO - [process_and_score.py:246] - Calculated trade imbalance score and normalized import and deficit scores.
2025-07-11 18:17:14,429 - INFO - [process_and_score.py:271] - Trade Imbalance Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\trade_imbalance_score.xlsx
2025-07-11 18:17:14,429 - INFO - [process_and_score.py:287] - --- Starting Employment Growth Score Calculation ---
2025-07-11 18:17:14,457 - INFO - [process_and_score.py:292] - Successfully loaded raw BLS employment data and the master sheet.
2025-07-11 18:17:14,459 - INFO - [process_and_score.py:298] - Aligning monthly employment data with latest GDP quarter: 2025-Q1
2025-07-11 18:17:14,465 - INFO - [process_and_score.py:309] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-11 18:17:14,465 - INFO - [process_and_score.py:310] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-11 18:17:14,485 - INFO - [process_and_score.py:348] - Calculated and normalized employment size and growth scores.
2025-07-11 18:17:14,555 - INFO - [process_and_score.py:363] - Employment Growth Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\employment_growth_score.xlsx
2025-07-11 18:17:14,559 - INFO - [process_and_score.py:379] - --- Starting Rolling 12M Avg. Score Calculation for FRED Indicator: Industry_Production ---
2025-07-11 18:17:14,598 - INFO - [process_and_score.py:384] - Successfully loaded raw FRED data.
2025-07-11 18:17:14,605 - INFO - [process_and_score.py:398] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-11 18:17:14,605 - INFO - [process_and_score.py:399] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-11 18:17:14,632 - INFO - [process_and_score.py:431] - Calculated and normalized rolling average growth for 79 series.
2025-07-11 18:17:14,690 - INFO - [process_and_score.py:451] - Indutsrial Output Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\industrial_output_score.xlsx
2025-07-11 18:17:14,690 - INFO - [process_and_score.py:379] - --- Starting Rolling 12M Avg. Score Calculation for FRED Indicator: Capacity_Utilization ---
2025-07-11 18:17:14,705 - INFO - [process_and_score.py:384] - Successfully loaded raw FRED data.
2025-07-11 18:17:14,721 - INFO - [process_and_score.py:398] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-11 18:17:14,721 - INFO - [process_and_score.py:399] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-11 18:17:14,748 - INFO - [process_and_score.py:431] - Calculated and normalized rolling average growth for 26 series.
2025-07-11 18:17:14,804 - INFO - [process_and_score.py:465] - Capacity Utilization Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\capacity_utilization_score.xlsx
2025-07-11 18:17:14,812 - INFO - [process_and_score.py:700] - ===== Data Processing and Scoring Pipeline Finished =====
