2025-07-02 18:21:47,899 - INFO - [logger.py:52] - <PERSON><PERSON> initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-02_18-21-47.log
2025-07-02 18:21:47,899 - INFO - [fetch_dataweb2.py:50] - --- Starting DataWeb Domestic Exports fetch for years: [2023, 2024, 2025] ---
2025-07-02 18:21:50,223 - ERROR - [fetch_dataweb2.py:66] - Failed to get data for Domestic Exports. Error: 429 Client Error: Too Many Requests for url: https://datawebws.usitc.gov/dataweb/api/v2/report2/runReport
2025-07-02 18:21:50,239 - INFO - [fetch_dataweb2.py:50] - --- Starting DataWeb Imports for Consumption fetch for years: [2023, 2024, 2025] ---
2025-07-02 18:21:53,387 - INFO - [fetch_dataweb2.py:88] - Successfully processed 3924 data points for Imports for Consumption.
2025-07-02 18:21:53,467 - INFO - [fetch_dataweb2.py:109] - DataWeb Imports for Consumption fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_imports_raw.csv
