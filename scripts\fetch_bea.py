import pandas as pd
import sys
from pathlib import Path
import beaapi

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config
from logger import log

def fetch_and_save_bea_data(years: list):
    """
    Fetches quarterly GDP by Industry data from the BEA API for a list of years.

    It filters the data to include only industries specified in the master sheet,
    handles deduplication, and saves the result to a CSV file.

    Args:
        years (list): A list of years (as strings or integers) to fetch data for.
    """
    log.info(f"Starting BEA GDP by Industry data fetch for years: {years}")

    try:
        master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH)
        target_bea_codes = master_sheet_df['BEA_code'].dropna().astype(str).unique().tolist()
    except FileNotFoundError:
        log.error(f"Master sheet not found at {config.MASTER_SHEET_PATH}. Aborting BEA fetch.")
        return
    except KeyError:
        log.error("'BEA_code' column not found in master sheet. Aborting BEA fetch.")
        return

    if not target_bea_codes:
        log.warning("No BEA codes found in master sheet. Aborting.")
        return

    log.info(f"Found {len(target_bea_codes)} unique BEA industry codes to filter for.")
    
    # The API expects the years as a comma-separated string
    year_str = ",".join(map(str, years))

    try:
        log.info("Requesting data from BEA API...")
        bea_df = beaapi.get_data(
            config.BEA_API_KEY,  # API key is the first argument
            datasetname='GDPbyIndustry',
            Frequency='Q',
            Industry='ALL',
            TableID='1',
            Year=year_str
        )
        log.info(f"Successfully received data from BEA. Total rows returned: {len(bea_df)}")

    except Exception as e:
        log.error(f"Failed to fetch data from BEA API. Error: {e}")
        return


    # 1. Filter for the industries we care about
    original_rows = len(bea_df)
    bea_df_filtered = bea_df[bea_df['Industry'].isin(target_bea_codes)].copy()
    log.info(f"Filtered {original_rows} rows down to {len(bea_df_filtered)} relevant industry rows.")

    if bea_df_filtered.empty:
        log.warning("No data found for the target BEA codes in the API response.")
        return

    # 2. Select and rename columns
    bea_df_filtered = bea_df_filtered[['Year', 'Quarter', 'Industry', 'IndustrYDescription', 'DataValue']]
    bea_df_filtered = bea_df_filtered.rename(columns={
        'Year': 'year',
        'Quarter': 'quarter',
        'Industry': 'bea_code',
        'IndustrYDescription': 'industry_description',
        'DataValue': 'gdp_value'
    })

    # 3. Clean the value column
    # bea_df_filtered['gdp_value'] = pd.to_numeric(bea_df_filtered['gdp_value'].str.replace(',', ''), errors='coerce')
    # bea_df_filtered.dropna(subset=['gdp_value'], inplace=True)
    bea_df_filtered['year'] = bea_df_filtered['year'].astype(int)
    bea_df_filtered['quarter'] = bea_df_filtered['quarter'].astype(str)
    bea_df_filtered['bea_code'] = bea_df_filtered['bea_code'].astype(str)

    # 4. Deduplication and Save Logic
    output_path = config.RAW_DATA_DIR / "bea_gdp_by_industry_raw.csv"
    final_df = bea_df_filtered

    if output_path.exists():
        log.info(f"Found existing BEA data at {output_path}. Merging and deduplicating.")
        old_df = pd.read_csv(output_path)
        old_df['year'] = old_df['year'].astype(int)
        old_df['bea_code'] = old_df['bea_code'].astype(str)
        old_df['quarter'] = old_df['quarter'].astype(str)
        combined_df = pd.concat([old_df, final_df], ignore_index=True)
        final_df = combined_df.drop_duplicates(subset=['year', 'quarter', 'bea_code'], keep='last')
        log.info(f"Combined BEA data size after deduplication: {len(final_df)} rows.")

    final_df = final_df.sort_values(by=['bea_code', 'year', 'quarter']).reset_index(drop=True)
    final_df.to_csv(output_path, index=False)
    log.info(f"BEA data fetch complete. Data saved to {output_path}")

if __name__ == '__main__':
    years_to_fetch = [2023, 2024, 2025]
    fetch_and_save_bea_data(years=years_to_fetch)