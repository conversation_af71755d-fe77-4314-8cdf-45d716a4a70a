import requests
import json
import pandas as pd
import sys
from pathlib import Path
from datetime import datetime

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config
from logger import log

# --- Helper functions ---
def get_data_from_groups(dataGroups):
    """Extracts row data from the response structure."""
    data = []
    for row in dataGroups:
        rowData = []
        for field in row['rowEntries']:
            rowData.append(field['value'])
        data.append(rowData)
    return data

def get_columns_from_groups(columnGroups, prevCols=None):
    """Recursively extracts column headers from the response structure."""
    columns = prevCols if prevCols is not None else []
    for group in columnGroups:
        if isinstance(group, dict) and 'columns' in group.keys():
            get_columns_from_groups(group['columns'], columns)
        elif isinstance(group, dict) and 'label' in group.keys():
            columns.append(group['label'])
        elif isinstance(group, list):
            get_columns_from_groups(group, columns)
    return columns
# --------------------------------------------------------------------

# --- THE GENERIC "WORKER" FUNCTION ---
def _fetch_and_process_dataweb(query_template: str, years: list, value_col_name: str, output_filename: str, log_label: str):
    """
    A generic worker function to fetch, process, and save data from the DataWeb API.

    Args:
        query_template (str): The JSON query string for the API.
        years (list): A list of years to inject into the query.
        value_col_name (str): The desired name for the final value column (e.g., 'export_value').
        output_filename (str): The name of the CSV file to save the data to.
        log_label (str): A label for logging messages (e.g., "Domestic Exports").
    """
    log.info(f"--- Starting DataWeb {log_label} fetch for years: {years} ---")
    #-- Load master sheet---
    try:
        master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH)
        # Ensure NAICS codes are strings for accurate matching
        target_naics_codes = master_sheet_df['NAICS_code'].dropna().astype(str).unique().tolist()
        log.info(f"Found {len(target_naics_codes)} unique NAICS codes in master sheet to filter for.")
    except FileNotFoundError:
        log.error(f"Master sheet not found at {config.MASTER_SHEET_PATH}. Aborting {log_label} fetch.")
        return


    # 1. Prepare the query
    query = json.loads(query_template)
    query['searchOptions']['componentSettings']['years'] = [str(y) for y in years]

    # 2. Make the API Call
    base_url = 'https://datawebws.usitc.gov/dataweb'
    headers = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + config.DATAWEB_TOKEN}
    requests.packages.urllib3.disable_warnings()

    try:
        response = requests.post(f'{base_url}/api/v2/report2/runReport', headers=headers, json=query, verify=False)
        response.raise_for_status()
        response_json = response.json()
    except requests.exceptions.RequestException as e:
        log.error(f"Failed to get data for {log_label}. Error: {e}")
        return

    # 3. Parse and Transform
    try:
        columns = get_columns_from_groups(response_json['dto']['tables'][0]['column_groups'])
        data = get_data_from_groups(response_json['dto']['tables'][0]['row_groups'][0]['rowsNew'])
        wide_df = pd.DataFrame(data, columns=columns)

        id_vars = ['NAIC Number', 'Description', 'Year']
        value_vars = [col for col in wide_df.columns if col not in id_vars]
        long_df = pd.melt(wide_df, id_vars=id_vars, value_vars=value_vars, var_name='month', value_name=value_col_name)

        # 4. Clean Data
        long_df = long_df.rename(columns={'NAIC Number': 'naics_code', 'Description': 'description', 'Year': 'year'})
        long_df[value_col_name] = pd.to_numeric(long_df[value_col_name].astype(str).str.replace(',', ''), errors='coerce')
        
        month_map = {name: i for i, name in enumerate(['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'], 1)}
        long_df['month_num'] = long_df['month'].map(month_map)
        long_df['date'] = pd.to_datetime(long_df['year'].astype(str) + '-' + long_df['month_num'].astype(str) + '-01').dt.date
        
        new_df = long_df[['date', 'naics_code', 'description', value_col_name]].copy()
        log.info(f"Successfully processed {len(new_df)} data points for {log_label}.")
        # -- filtering based on master sheet --
        log.info(f"Filtering {log_label} data based on master sheet NAICS codes...")
        # Ensure the `naics_code` column is a string before filtering
        new_df['naics_code'] = new_df['naics_code'].astype(str)
        
        original_rows = len(new_df)
        new_df = new_df[new_df['naics_code'].isin(target_naics_codes)].copy()
        log.info(f"Filtered {original_rows} rows down to {len(new_df)} relevant NAICS rows.")

    except (KeyError, IndexError) as e:
        log.error(f"Could not parse the JSON response for {log_label}. Error: {e}")
        return

    # 5. Deduplicate and Save
    output_path = config.RAW_DATA_DIR / output_filename
    new_df['naics_code'] = new_df['naics_code'].astype(str)

    if output_path.exists():
        old_df = pd.read_csv(output_path)
        old_df['date'] = pd.to_datetime(old_df['date']).dt.date
        old_df['naics_code'] = old_df['naics_code'].astype(str)
        combined_df = pd.concat([old_df, new_df], ignore_index=True)
        final_df = combined_df.drop_duplicates(subset=['date', 'naics_code'], keep='last')
    else:
        final_df = new_df

    final_df = final_df.sort_values(by=['naics_code', 'date']).reset_index(drop=True)
    final_df.to_csv(output_path, index=False)
    log.info(f"DataWeb {log_label} fetch complete. Data saved to {output_path}")

# --- SPECIFIC FUNCTIONS THAT CALL THE WORKER ---

def fetch_dataweb_exports(years: list):
    """Fetches Domestic Exports data."""
    query = """
    {"savedQueryType":"","savedQueryName":"Domestic export","savedQueryDesc":"","isOwner":true,"runMonthly":false,"unitConversion":"0","manualConversions":[],"reportOptions":{"tradeType":"Export","classificationSystem":"NAIC"},"searchOptions":{"MiscGroup":{"districts":{"aggregation":"Aggregate District","districtGroups":{"userGroups":[]},"districts":[],"districtsExpanded":[{"name":"All Districts","value":"all"}],"districtsSelectType":"all"},"importPrograms":{"aggregation":null,"importPrograms":[],"programsSelectType":"all"},"extImportPrograms":{"aggregation":"Aggregate CSC","extImportPrograms":[],"extImportProgramsExpanded":[],"programsSelectType":"all"},"provisionCodes":{"aggregation":"Aggregate RPCODE","provisionCodesSelectType":"all","rateProvisionCodes":[],"rateProvisionCodesExpanded":[],"rateProvisionGroups":{"systemGroups":[]}}},"commodities":{"aggregation":"Break Out Commodities","codeDisplayFormat":"YES","commodities":[],"commoditiesExpanded":[],"commoditiesManual":"","commodityGroups":{"systemGroups":[],"userGroups":[]},"commoditySelectType":"all","granularity":"4","groupGranularity":null,"searchGranularity":null,"showHTSValidDetails":""},"componentSettings":{"dataToReport":["FAS_VALUE"],"scale":"1","timeframeSelectType":"fullYears","years":[],"startDate":null,"endDate":null,"startMonth":null,"endMonth":null,"yearsTimeline":"Monthly"},"countries":{"aggregation":"Aggregate Countries","countries":[],"countriesExpanded":[{"name":"All Countries","value":"all"}],"countriesSelectType":"all","countryGroups":{"systemGroups":[],"userGroups":[]}}},"sortingAndDataFormat":{"DataSort":{"columnOrder":["NAICS4 & DESCRIPTION","YEAR"],"fullColumnOrder":[{"hasChildren":false,"name":"NAICS4 & DESCRIPTION","value":"NAICS4 & DESCRIPTION","classificationSystem":"","groupUUID":"","items":[],"tradeType":""},{"hasChildren":false,"name":"Year","value":"YEAR","classificationSystem":"","groupUUID":"","items":[],"tradeType":""}],"sortOrder":[{"sortData":"NAICS4 & DESCRIPTION","orderBy":"asc","year":""}]},"reportCustomizations":{"exportCombineTables":false,"totalRecords":"20000","exportRawData":false}},"deletedCountryUserGroups":[],"deletedCommodityUserGroups":[],"deletedDistrictUserGroups":[]}
    """
    _fetch_and_process_dataweb(
        query_template=query,
        years=years,
        value_col_name='export_value',
        output_filename='dataweb_exports_raw.csv',
        log_label='Domestic Exports'
    )

def fetch_dataweb_imports(years: list):
    """Fetches Imports for Consumption data."""
    query = """{"savedQueryType":"","savedQueryName":"","savedQueryDesc":"","isOwner":true,"runMonthly":false,"unitConversion":"0","manualConversions":[],"reportOptions":{"tradeType":"Import","classificationSystem":"NAIC"},"searchOptions":{"MiscGroup":{"districts":{"aggregation":"Aggregate District","districtGroups":{"userGroups":[]},"districts":[],"districtsExpanded":[{"name":"All Districts","value":"all"}],"districtsSelectType":"all"},"importPrograms":{"aggregation":null,"importPrograms":[],"programsSelectType":"all"},"extImportPrograms":{"aggregation":"Aggregate CSC","extImportPrograms":[],"extImportProgramsExpanded":[],"programsSelectType":"all"},"provisionCodes":{"aggregation":"Aggregate RPCODE","provisionCodesSelectType":"all","rateProvisionCodes":[],"rateProvisionCodesExpanded":[],"rateProvisionGroups":{"systemGroups":[]}}},"commodities":{"aggregation":"Break Out Commodities","codeDisplayFormat":"YES","commodities":[],"commoditiesExpanded":[],"commoditiesManual":"","commodityGroups":{"systemGroups":[],"userGroups":[]},"commoditySelectType":"all","granularity":"4","groupGranularity":null,"searchGranularity":null,"showHTSValidDetails":""},"componentSettings":{"dataToReport":["CONS_CUSTOMS_VALUE"],"scale":"1","timeframeSelectType":"fullYears","years":["2025","2024","2023"],"startDate":null,"endDate":null,"startMonth":null,"endMonth":null,"yearsTimeline":"Monthly"},"countries":{"aggregation":"Aggregate Countries","countries":[],"countriesExpanded":[{"name":"All Countries","value":"all"}],"countriesSelectType":"all","countryGroups":{"systemGroups":[],"userGroups":[]}}},"sortingAndDataFormat":{"DataSort":{"columnOrder":["NAICS4 & DESCRIPTION","YEAR"],"fullColumnOrder":[{"hasChildren":false,"name":"NAICS4 & DESCRIPTION","value":"NAICS4 & DESCRIPTION","classificationSystem":"","groupUUID":"","items":[],"tradeType":""},{"hasChildren":false,"name":"Year","value":"YEAR","classificationSystem":"","groupUUID":"","items":[],"tradeType":""}],"sortOrder":[{"sortData":"NAICS4 & DESCRIPTION","orderBy":"asc","year":""}]},"reportCustomizations":{"exportCombineTables":false,"totalRecords":"20000","exportRawData":false}},"deletedCountryUserGroups":[],"deletedCommodityUserGroups":[],"deletedDistrictUserGroups":[]}""" 
    _fetch_and_process_dataweb(
        query_template=query,
        years=years,
        value_col_name='import_value',
        output_filename='dataweb_imports_raw.csv',
        log_label='Imports for Consumption'
    )

if __name__ == '__main__':
    years_to_fetch = [2023, 2024, 2025]
    
    # Run both fetchers
    fetch_dataweb_exports(years=years_to_fetch)
    # fetch_dataweb_imports(years=years_to_fetch)