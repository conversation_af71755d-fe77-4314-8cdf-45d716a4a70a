import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config
from logger import log


# ==============================================================================
# === HELPER FUNCTION FOR NORMALIZATION ========================================
# ==============================================================================

def normalize_with_percentile_capping(data_series: pd.Series) -> pd.Series:
    """
    Normalizes a pandas Series using 10th and 90th percentile capping.

    - Values <= 10th percentile are mapped to 0.
    - Values >= 90th percentile are mapped to 1.
    - Values in between are min-max scaled between 0 and 1.

    Args:
        data_series (pd.Series): The column of data to normalize.

    Returns:
        pd.Series: The new column with normalized scores.
    """
    # Calculate the 10th and 90th percentile thresholds
    p10 = data_series.quantile(0.10)
    p90 = data_series.quantile(0.90)

    # Apply the three-part rule using np.select for efficiency
    import numpy as np
    conditions = [
        data_series <= p10,
        data_series >= p90,
    ]
    choices = [
        0.0,
        1.0,
    ]
    # The default case is the scaled value for everything in between
    scaled_values = (data_series - p10) / (p90 - p10)
    
    return np.select(conditions, choices, default=scaled_values)

# ==============================================================================
# === FUNCTION FOR ECONOMIC VALUE ADD SCORE ==================================
# ==============================================================================

def calculate_economic_value_add_score(gdp_weight=0.6, growth_weight=0.4):
    """
    Loads BEA GDP data, calculates recent growth and a normalized GDP score,
    and combines them into an 'Economic Value Add Score'.
    
    The scores for GDP and growth are both min-max normalized before being weighted.
    Saves the result to the processed data directory.

    Args:
        gdp_weight (float): The weight for the normalized GDP size score.
        growth_weight (float): The weight for the normalized growth score.
    """
    log.info("--- Starting Economic Value Add Score Calculation ---")

    if gdp_weight + growth_weight != 1.0:
        log.warning(f"Weights do not sum to 1.0 (gdp: {gdp_weight}, growth: {growth_weight}).")

    # --- 1. Load Data ---
    try:
        gdp_df = pd.read_csv(config.RAW_DATA_DIR / "bea_gdp_by_industry_raw.csv")
        log.info("Successfully loaded raw BEA GDP data and the master sheet.")
    except FileNotFoundError as e:
        log.error(f"Could not find required data file. Error: {e}. Aborting.")
        return
    
    # mapping of code to descrition from the raw data
    description_mapping = gdp_df[['bea_code', 'industry_description']].drop_duplicates()

    # --- 2. Determine Analysis Window ---
    # Create a sortable value from year and quarter (e.g., 2023-Q4)
    quarter_map = {'I': 1, 'II': 2, 'III': 3, 'IV': 4}
    gdp_df['quarter_num'] = gdp_df['quarter'].map(quarter_map)

    gdp_df['year_quarter'] = gdp_df['year'].astype(str) + '-Q' + gdp_df['quarter_num'].astype(str)
    
    
    # Find the latest quarter that is present for ALL industries in our dataset
    latest_quarter = gdp_df.groupby('bea_code')['year_quarter'].max().min()
    log.info(f"Latest complete quarter available for all industries: {latest_quarter}")

    # Create a DataFrame of all unique quarters and sort them
    all_quarters_sorted = pd.DataFrame({'year_quarter': gdp_df['year_quarter'].unique()}).sort_values('year_quarter', ascending=False).reset_index(drop=True)
    
    # Find the index of our latest quarter
    latest_quarter_index = all_quarters_sorted[all_quarters_sorted['year_quarter'] == latest_quarter].index[0]
    
    # Select the 8 quarters we need based on the index
    # Recent 4Q are from index `latest_quarter_index` to `index+3`
    # Previous 4Q are from index `index+4` to `index+7`
    if len(all_quarters_sorted) < latest_quarter_index + 8:
        log.error("Not enough historical data (less than 8 quarters) to perform calculation. Aborting.")
        return
        
    recent_4q_list = all_quarters_sorted.loc[latest_quarter_index : latest_quarter_index + 3, 'year_quarter'].tolist()
    previous_4q_list = all_quarters_sorted.loc[latest_quarter_index + 4 : latest_quarter_index + 7, 'year_quarter'].tolist()

    log.info(f"Recent 4 Quarters for analysis: {recent_4q_list}")
    log.info(f"Previous 4 Quarters for analysis: {previous_4q_list}")

    # --- 3. Pivot Data to Wide Format ---
    gdp_pivot = gdp_df.pivot_table(index='bea_code', columns='year_quarter', values='gdp_value')
    
    # --- 4. Calculate GDP Sums and Growth ---
    gdp_pivot['GDP_Recent4Q'] = gdp_pivot[recent_4q_list].sum(axis=1)
    gdp_pivot['GDP_Previous4Q'] = gdp_pivot[previous_4q_list].sum(axis=1)
    
    # Calculate growth, handling potential division by zero
    gdp_pivot['recent_growth_pct'] = (gdp_pivot['GDP_Recent4Q'] - gdp_pivot['GDP_Previous4Q']) / gdp_pivot['GDP_Previous4Q'] * 100
    gdp_pivot['recent_growth_pct'] = gdp_pivot['recent_growth_pct'].fillna(0)
    # gdp_pivot['recent_growth_pct'].fillna(0, inplace=True) # Fill NaN with 0 if previous GDP was 0

    # --- 5. Normalize Scores (Min-Max Scaling) ---
    # Normalize GDP size
    gdp_pivot['gdp_score_normalized'] = normalize_with_percentile_capping(gdp_pivot['GDP_Recent4Q'])

    # Normalize growth percentage
    gdp_pivot['growth_score_normalized'] = normalize_with_percentile_capping(gdp_pivot['recent_growth_pct'])
    log.info("Calculated and normalized GDP size and growth scores.")

    # --- 6. Calculate Final Economic Heft Score ---
    gdp_pivot['economic_value_add_score'] = (
        gdp_weight * gdp_pivot['gdp_score_normalized'] +
        growth_weight * gdp_pivot['growth_score_normalized']
    )

    # --- 7. Prepare and Save Final Output ---
    # Reset index to turn 'bea_code' back into a column
    final_scores_df = gdp_pivot.reset_index()

    # Ensure types are consistent before merging
    final_scores_df['bea_code'] = final_scores_df['bea_code'].astype(str)
    description_mapping['bea_code'] = description_mapping['bea_code'].astype(str)

    # merge to add the industry description
    final_output_df = pd.merge(
        final_scores_df,
        description_mapping,
        on='bea_code',
        how='left'
    )

    # pick relevant columns
    final_cols = [
        'bea_code', 'industry_description', 'GDP_Recent4Q', 'GDP_Previous4Q',
        'recent_growth_pct', 'gdp_score_normalized', 
        'growth_score_normalized', 'economic_value_add_score'
    ]
    final_output_df = final_output_df[final_cols]
    final_output_df = final_output_df.round(4) 

    # Save to the processed directory
    output_path = config.PROCESSED_DATA_DIR / "economic_value_add_score.xlsx"
    final_output_df.to_excel(output_path, index=False)
    log.info(f"Economic Value Add Score calculation complete. Results saved to {output_path}")

    return latest_quarter 


# ==============================================================================
# === FUNCTION FOR IMPORT INTENSITY SCORE ==================================
# ==============================================================================

def calculate_trade_imbalance_score(import_weight=0.5, deficit_weight=0.5):
    """
    Calculates the Trade Imbalance Score based on the latest available 12 months
    of trade data. This function is self-anchoring and does not depend on GDP data.

    Args:
        import_weight (float): The weight for the normalized import value.
        deficit_weight (float): The weight for the normalized trade deficit.
    """
    log.info("--- Starting Trade Imbalance Score Calculation ---")

    # --- 1. Load Data ---
    try:
        imports_df = pd.read_csv(config.RAW_DATA_DIR / "dataweb_imports_raw.csv")
        exports_df = pd.read_csv(config.RAW_DATA_DIR / "dataweb_exports_raw.csv")
        log.info("Successfully loaded raw DataWeb imports and exports data.")
    except FileNotFoundError as e:
        log.error(f"Could not find required trade data file. Error: {e}. Aborting.")
        return

    # --- 2. Determine 12-Month Analysis Window ---
    imports_df['date'] = pd.to_datetime(imports_df['date']).dt.date
    exports_df['date'] = pd.to_datetime(exports_df['date']).dt.date
    # Find the latest month available in the imports data (we assume it's the same for exports)
    latest_month = imports_df['date'].max()
    if pd.isna(latest_month):
        log.error("No valid dates found in the trade data. Aborting score calculation.")
        return
    log.info(f"Latest month found in trade data: {latest_month}. Using this as the anchor.")
    # Generate the list of the 12 specific start-of-month dates we need.
    end_date_of_window = pd.to_datetime(latest_month)
    # Create the list of 12 monthly dates ending in the latest_month
    monthly_dates = [
        (end_date_of_window - pd.DateOffset(months=i)).date() for i in range(12)
    ]
    log.info(f"Analysis window covers 12 months from {min(monthly_dates)} to {max(monthly_dates)}.")

    # --- 3. Filter and Aggregate Data ---
    # Filter both dataframes for the 12-month window
    imports_filtered = imports_df[imports_df['date'].isin(monthly_dates)]
    exports_filtered = exports_df[exports_df['date'].isin(monthly_dates)]

    # Check if we found data for the required period
    if imports_filtered.empty or exports_filtered.empty:
        log.error("No import or export data found for the required 12-month period. Aborting score calculation.")
        return
    
    # Group by NAICS code and sum the values to get totals for the period
    import_totals = imports_filtered.groupby('naics_code')['import_value'].sum()
    export_totals = exports_filtered.groupby('naics_code')['export_value'].sum()
    
    # Combine into a single DataFrame
    trade_df = pd.DataFrame({'Total_Imports_4Q': import_totals, 'Total_Exports_4Q': export_totals}).fillna(0)

    # --- 4. Calculate, Normalize, and Score ---
    trade_df['Trade_Deficit_4Q'] = trade_df['Total_Imports_4Q'] - trade_df['Total_Exports_4Q']
    
    # Normalize Total Imports
    trade_df['imports_normalized'] = normalize_with_percentile_capping(trade_df['Total_Imports_4Q'])
        
    # Normalize Trade Deficit
    trade_df['deficit_normalized'] = normalize_with_percentile_capping(trade_df['Trade_Deficit_4Q'])

    # Calculate final score
    trade_df['trade_imbalance_score'] = (
        import_weight * trade_df['imports_normalized'] +
        deficit_weight * trade_df['deficit_normalized']
    )
    log.info("Calculated trade imbalance score and normalized import and deficit scores.")
    
    # --- 5. Prepare and Save Final Output ---
    final_scores_df = trade_df.reset_index()


    # Get descriptions from the raw import data file
    description_mapping = imports_df[['naics_code', 'description']].drop_duplicates()
    
    # Ensure types are consistent for merging
    final_scores_df['naics_code'] = final_scores_df['naics_code'].astype(str)
    description_mapping['naics_code'] = description_mapping['naics_code'].astype(str)

    final_output_df = pd.merge(final_scores_df, description_mapping, on='naics_code', how='left')
    
    # Reorder columns
    final_cols = [
        'naics_code', 'description', 'Total_Imports_4Q', 'Total_Exports_4Q', 'Trade_Deficit_4Q',
        'imports_normalized', 'deficit_normalized', 'trade_imbalance_score'
    ]
    final_output_df = final_output_df[final_cols]
    final_output_df = final_output_df.round(4)
    
    output_path = config.PROCESSED_DATA_DIR / "trade_imbalance_score.xlsx"
    final_output_df.to_excel(output_path, index=False)
    log.info(f"Trade Imbalance Score calculation complete. Results saved to {output_path}")


# ==============================================================================
# === FUNCTION FOR EMPLOYMENT SCORE ========================================
# ==============================================================================

def calculate_employment_growth_score(emp_weight=0.6, growth_weight=0.4):
    """
    Calculates the Employment Growth Score based on the latest available 24 months
    of BLS data. This function is self-anchoring.

    Args:
        emp_weight (float): The weight for the normalized recent employment size.
        growth_weight (float): The weight for the normalized employment growth.
    """
    log.info("--- Starting Employment Growth Score Calculation ---")

    # --- 1. Load Data ---
    try:
        bls_df = pd.read_csv(config.RAW_DATA_DIR / "bls_employment_data_raw.csv")
        log.info("Successfully loaded raw BLS employment data and the master sheet.")
        master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH)
    except FileNotFoundError as e:
        log.error(f"Could not find required data file. Error: {e}. Aborting.")
        return

    # --- 2. Determine 24-Month Analysis Window ---
    # Create a proper date column in the BLS DataFrame
    bls_df['month'] = bls_df['period'].str.replace('M', '').astype(int)
    bls_df['date'] = pd.to_datetime(bls_df['year'].astype(str) + '-' + bls_df['month'].astype(str) + '-01').dt.date

    # Find the latest month that is present for ALL series in the dataset
    latest_month = bls_df.groupby('seriesID')['date'].max().min()

    if pd.isna(latest_month):
        log.error("No valid dates found in the BLS data. Aborting score calculation.")
        return
    
    log.info(f"Latest complete month found in BLS data: {latest_month}. Using this as the anchor.")
    # Generate a list of the last 24 months
    end_date_of_window = pd.to_datetime(latest_month)
    all_24m_dates = [(end_date_of_window - pd.DateOffset(months=i)).date() for i in range(24)]
    
    # Split the list into two 12-month periods
    recent_12m_dates = all_24m_dates[:12]
    previous_12m_dates = all_24m_dates[12:]
    
    log.info(f"Recent 12-month window: {min(recent_12m_dates)} to {max(recent_12m_dates)}")
    log.info(f"Previous 12-month window: {min(previous_12m_dates)} to {max(previous_12m_dates)}")

    # --- 3. Prepare and Filter Data ---
    # Filter for the two time periods
    recent_emp_df = bls_df[bls_df['date'].isin(recent_12m_dates)]
    previous_emp_df = bls_df[bls_df['date'].isin(previous_12m_dates)]

    # --- 4. Aggregate Data ---
    recent_emp_avg = recent_emp_df.groupby('seriesID')['value'].mean() # we should combine as per series id
    previous_emp_avg = previous_emp_df.groupby('seriesID')['value'].mean()

    # Combine into a single DataFrame
    emp_totals_df = pd.DataFrame({'Recent_Emp_12M': recent_emp_avg, 'Previous_Emp_12M': previous_emp_avg}).fillna(0)

    # --- 5. Calculate, Normalize, and Score ---
    emp_totals_df['recent_emp_growth_pct'] = (emp_totals_df['Recent_Emp_12M'] - emp_totals_df['Previous_Emp_12M']) / emp_totals_df['Previous_Emp_12M'] * 100
    emp_totals_df['recent_emp_growth_pct'] = emp_totals_df['recent_emp_growth_pct'].fillna(0)

    # Normalize Recent Employment Sum
    emp_totals_df['emp_score_normalized'] = normalize_with_percentile_capping(emp_totals_df['Recent_Emp_12M'])

    # Normalize Growth Percentage
    emp_totals_df['growth_score_normalized'] = normalize_with_percentile_capping(emp_totals_df['recent_emp_growth_pct'])

    # Calculate final score
    emp_totals_df['employment_growth_score'] = (
        emp_weight * emp_totals_df['emp_score_normalized'] +
        growth_weight * emp_totals_df['growth_score_normalized']
    )
    log.info("Calculated and normalized employment size and growth scores.")

    # --- 6. Prepare and Save Final Output ---
    final_scores_df = emp_totals_df.reset_index()

    # Define the final, simple column list
    final_cols = [
        'seriesID', 'Recent_Emp_12M', 'Previous_Emp_12M',
        'recent_emp_growth_pct', 'emp_score_normalized', 'growth_score_normalized', 'employment_growth_score'
    ]
    final_output_df = final_scores_df[final_cols]
    final_output_df = final_output_df.round(4)

    output_path = config.PROCESSED_DATA_DIR / "employment_growth_score.xlsx"
    final_output_df.to_excel(output_path, index=False)
    log.info(f"Employment Growth Score calculation complete. Results saved to {output_path}")


# ==============================================================================
# === FUNCTIONS FOR FRED BASED INDUSTRIAL CAPACITY UTILIZATION AND PRODUCTION SCORES =============================
# ==============================================================================

def _calculate_fred_yoy_score(indicator_name: str):
    """
    Generic worker function to calculate a rolling 12-month average vs prior 
    12-month average growth score for a given FRED indicator. This function is self-anchoring.

    Args:
        indicator_name (str): The indicator to filter for from the FRED data file.
    """
    log.info(f"--- Starting Rolling 12M Avg. Score Calculation for FRED Indicator: {indicator_name} ---")

    # --- 1. Load Data ---
    try:
        fred_df = pd.read_csv(config.RAW_DATA_DIR / "fred_data_raw.csv")
        log.info("Successfully loaded raw FRED data")
    except FileNotFoundError as e:
        log.error(f"Could not find FRED data file. Error: {e}. Aborting.")
        return

    # --- 2. Determine 24 Months Analysis window ---
    # First, filter for only the indicator we care about
    indicator_df = fred_df[fred_df['indicator'] == indicator_name].copy()
    if indicator_df.empty:
        log.warning(f"No data found for indicator '{indicator_name}' in fred_data_raw.csv. Aborting.")
        return
    # Find the latest complete month for this specific indicator
    latest_month = indicator_df.groupby('series_id')['date'].max().min()
    if pd.isna(latest_month):
        log.error(f"No valid dates found for indicator '{indicator_name}'. Aborting.")
        return
    log.info(f"Latest complete month for {indicator_name}: {latest_month}. Using this as the anchor.")

    # Generate a list of the last 24 months
    end_date_of_window = pd.to_datetime(latest_month)
    all_24m_dates = [(end_date_of_window - pd.DateOffset(months=i)).date() for i in range(24)]

    # Split the list into two 12-month periods
    recent_12m_dates = all_24m_dates[:12]
    previous_12m_dates = all_24m_dates[12:]
    
    log.info(f"Recent 12-month window: {min(recent_12m_dates)} to {max(recent_12m_dates)}")
    log.info(f"Previous 12-month window: {min(previous_12m_dates)} to {max(previous_12m_dates)}")

    # --- 3. Filter, Prepare and Aggregate Data ---
    fred_df['date'] = pd.to_datetime(fred_df['date']).dt.date
    
    # Filter for the specific indicator we're calculating
    
    # Filter for the two time periods
    recent_df = indicator_df[indicator_df['date'].isin(recent_12m_dates)]
    previous_df = indicator_df[indicator_df['date'].isin(previous_12m_dates)]

    # Calculate the average value for each series ID over each period
    recent_avg = recent_df.groupby('series_id')['value'].mean().rename('recent_12m_avg')
    previous_avg = previous_df.groupby('series_id')['value'].mean().rename('previous_12m_avg')
    
    # Combine the data into one DataFrame
    avg_df = pd.concat([recent_avg, previous_avg], axis=1)
    avg_df.dropna(inplace=True)

    if avg_df.empty:
        log.warning(f"No series found with complete data for both 12-month periods. Aborting for {indicator_name}.")
        return

    # --- 4. Calculate Growth and Normalize ---
    avg_df['avg_growth_pct'] = (avg_df['recent_12m_avg'] - avg_df['previous_12m_avg']) / avg_df['previous_12m_avg'] * 100

    avg_df['avg_growth_pct'] = avg_df['avg_growth_pct'].fillna(0)

    # Normalize the growth percentage using our helper function
    avg_df['yoy_growth_score'] = normalize_with_percentile_capping(avg_df['avg_growth_pct'])
    
    log.info(f"Calculated and normalized rolling average growth for {len(avg_df)} series.")

    # --- 5. Prepare and Return Final DataFrame ---
    final_scores_df = avg_df.reset_index()
    
    final_cols = ['series_id', 'recent_12m_avg', 'previous_12m_avg', 'avg_growth_pct', 'yoy_growth_score']
    final_scores_df = final_scores_df[final_cols].round(4)
    
    return final_scores_df
    

def calculate_industrial_output_score(output_filename='industrial_output_score.xlsx'):
    """Wrapper function to calculate the Industrial Output YoY score."""
    final_scores_df = _calculate_fred_yoy_score(
        indicator_name='Industry_Production'
    )
    final_scores_df.rename(columns = {'yoy_growth_score': 'industrial_output_score'}, inplace=True)
    output_path = config.PROCESSED_DATA_DIR / output_filename
    final_scores_df.to_excel(output_path, index=False)
    log.info(f"Indutsrial Output Score calculation complete. Results saved to {output_path}")
    


def calculate_capacity_utilization_score(output_filename='capacity_utilization_score.xlsx'):
    """Wrapper function to calculate the Industrial Capacity YoY score."""
    final_scores_df = _calculate_fred_yoy_score(
        indicator_name='Capacity_Utilization',
    )
    final_scores_df.rename(columns = {'yoy_growth_score': 'capacity_utilization_score'}, inplace=True)
    output_path = config.PROCESSED_DATA_DIR / output_filename
    final_scores_df.to_excel(output_path, index=False)
    log.info(f"Capacity Utilization Score calculation complete. Results saved to {output_path}")


# ==============================================================================
# === FUNCTION FOR FINAL SCORE COMBINATION =============================
# ==============================================================================

def create_final_report():
    """
    Loads all individual processed score files and the master sheet,
    merges them to populate scores, and saves the final report.
    """
    log.info("--- Starting Final Report Combination ---")

    try:
        # --- 1. Load All Data Files ---
        master_df = pd.read_csv(config.MASTER_SHEET_PATH)
        log.info(f"Loaded master sheet with {len(master_df)} rows.")

        eco_df = pd.read_excel(config.PROCESSED_DATA_DIR / "economic_value_add_score.xlsx")
        trade_imb_df = pd.read_excel(config.PROCESSED_DATA_DIR / "trade_imbalance_score.xlsx")
        employment_df = pd.read_excel(config.PROCESSED_DATA_DIR / "employment_growth_score.xlsx")
        prod_df = pd.read_excel(config.PROCESSED_DATA_DIR / "industrial_output_score.xlsx")
        cap_df = pd.read_excel(config.PROCESSED_DATA_DIR / "capacity_utilization_score.xlsx")
        log.info("Successfully loaded all individual score files.")

    except FileNotFoundError as e:
        log.error(f"Could not find a required data or score file. Error: {e}. Aborting report creation.")
        return

    # Create a copy to work with, preserving the original
    final_report_df = master_df.copy()

    # --- 2. Standardize Column Names and Prepare for Merging ---
    
    # Standardize master sheet columns (lowercase for easy matching)
    final_report_df.columns = [col.lower() for col in final_report_df.columns]

    # Prepare heft scores
    eco_value_scores_to_merge = eco_df[['bea_code', 'economic_value_add_score']].copy()

    # Prepare import scores
    trade_imb_scores_to_merge = trade_imb_df[['naics_code', 'trade_imbalance_score']].copy()

    # Prepare employment scores
    employment_df.rename(columns={'seriesID': 'seasonal_adj_bls_series_id'}, inplace=True)
    employment_scores_to_merge = employment_df[['seasonal_adj_bls_series_id', 'employment_growth_score']].copy()

    # Prepare production scores
    prod_df.rename(columns={'series_id': 'seriesid_industry_production(m)'}, inplace=True)
    prod_scores_to_merge = prod_df[['seriesid_industry_production(m)', 'industrial_output_score']].copy()

    # Prepare capacity scores
    cap_df.rename(columns={'series_id': 'seriesid_capacity_utilization(m)'}, inplace=True)
    cap_scores_to_merge = cap_df[['seriesid_capacity_utilization(m)', 'capacity_utilization_score']].copy()

    # Ensure all key columns are strings to prevent merge errors
    for df, col in [
        (final_report_df, 'bea_code'), (eco_value_scores_to_merge, 'bea_code'),
        (final_report_df, 'naics_code'), (trade_imb_scores_to_merge, 'naics_code'),
        (final_report_df, 'seasonal_adj_bls_series_id'), (employment_scores_to_merge, 'seasonal_adj_bls_series_id'),
        (final_report_df, 'seriesid_industry_production(m)'), (prod_scores_to_merge, 'seriesid_industry_production(m)'),
        (final_report_df, 'seriesid_capacity_utilization(m)'), (cap_scores_to_merge, 'seriesid_capacity_utilization(m)'),
    ]:
        df[col] = df[col].astype(str)

    # --- 3. Sequential Left Merges ---
    log.info("Merging scores into the final report...")
    
    final_report_df = pd.merge(final_report_df, eco_value_scores_to_merge, on='bea_code', how='left')
    final_report_df = pd.merge(final_report_df, trade_imb_scores_to_merge, on='naics_code', how='left')
    final_report_df = pd.merge(final_report_df, employment_scores_to_merge, on='seasonal_adj_bls_series_id', how='left')
    final_report_df = pd.merge(final_report_df, prod_scores_to_merge, on='seriesid_industry_production(m)', how='left')
    final_report_df = pd.merge(final_report_df, cap_scores_to_merge, on='seriesid_capacity_utilization(m)', how='left')

    log.info("All scores have been merged.")
    
    # --- 4. Calculate Combined and Final Scores ---
    log.info("Calculating Industrial output and capacity utilization score and the final overall score...")
    final_report_df['industrial_output_and_capacity_utilization_score'] = final_report_df[
        ['industrial_output_score', 'capacity_utilization_score']
    ].mean(axis=1, skipna=True)

    # Calculate US Manufacturing Momentum Score (average of the four main score components)
    log.info("Calculating US Manufacturing Momentum Score...")
    final_report_df['us_manufacturing_momentum_score'] = final_report_df[
        ['economic_value_add_score', 'trade_imbalance_score', 'employment_growth_score', 'industrial_output_and_capacity_utilization_score']
    ].mean(axis=1, skipna=True)

    # Making Internal final scoresheet
    # column drop and reordering
    cols_drop = [
        'seriesid_industry_production(a)',
        'seriesid_capacity_utilization(a)',
        'seriesid_industrial_capacity(a)',
        'seasonal_unadj_bls_series_id',
    ]
    existing_cols_to_drop = [col for col in cols_drop if col in final_report_df.columns]
    final_report_df.drop(columns=existing_cols_to_drop, inplace=True)
    final_report_df = final_report_df[['naics_code', 'naics_industry_description', 'trade_imbalance_score','bea_code', 'bea_industry_description',
        'economic_value_add_score','seasonal_adj_bls_series_id', 'employment_growth_score','seriesid_industry_production(m)', 'industrial_output_score','seriesid_capacity_utilization(m)',
        'capacity_utilization_score', 'industrial_output_and_capacity_utilization_score', 'us_manufacturing_momentum_score'
    ]]
    final_report_df = final_report_df.round(4)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"internal_us_manufacturing_momentum_score_{timestamp}.xlsx"
    output_path = config.FINAL_REPORTS_DIR / output_filename

    final_report_df.to_excel(output_path, index=False)
    log.info(f"Internal Final report created successfully and saved to {output_path}")

    # --- 7. Preparing final report ---
    log.info("Cleaning up final report columns...")
    cols_to_drop = [
        'bea_code',
        'bea_industry_description',
        'seriesid_industry_production(m)',
        'seriesid_capacity_utilization(m)',
        'seriesid_industrial_capacity(m)',
        'seasonal_adj_bls_series_id',
        'industrial_output_score',
        'capacity_utilization_score'
    ]
    # Drop only the columns that actually exist in the DataFrame to avoid errors
    existing_cols_to_drop = [col for col in cols_to_drop if col in final_report_df.columns]
    final_report_df.drop(columns=existing_cols_to_drop, inplace=True)

    # --- 8. Save the Final Report ---
    output_filename = f"us_manufacturing_momentum_score_{timestamp}.xlsx"
    output_path = config.FINAL_REPORTS_DIR / output_filename

    final_report_df.to_excel(output_path, index=False)
    log.info(f"Final report created successfully and saved to {output_path}")
    return final_report_df


# ==============================================================================
# === FUNCTION FOR FACTSET ROLL-UP AGGREGATION =================================
# ==============================================================================

def create_factset_rollup_report(naics_scores_df: pd.DataFrame):
    """
    Takes the final NAICS-level scores DataFrame and a FactSet mapping file,
    then aggregates the scores up to the FactSet Subgroup level by averaging.
    """
    log.info("--- Starting FactSet Subgroup Roll-up Calculation ---")

    try:
        # --- 1. Load Input Files ---
        factset_mapping_df = pd.read_excel(config.FACTSET_MAPPING_PATH)
        log.info("Successfully loaded NAICS-level scores and FactSet mapping file.")

    except FileNotFoundError as e:
        log.error(f"Could not find a required data file. Error: {e}. Aborting FactSet roll-up.")
        return

    # --- 2. Prepare Data for Merging ---
    
    # Ensure the join key ('naics_code') is the same data type in both frames
    naics_scores_df['naics_code'] = naics_scores_df['naics_code'].astype(str)
    factset_mapping_df['naics_code'] = factset_mapping_df['naics_code'].astype(str)
    
    # --- 3. Merge the two DataFrames ---
    merged_for_rollup = pd.merge(
        factset_mapping_df,
        naics_scores_df,
        on='naics_code',
        how='left'
    )
    # Drop any rows from the mapping that didn't find a matching score
    merged_for_rollup.dropna(subset=['us_manufacturing_momentum_score'], inplace=True)
    
    if merged_for_rollup.empty:
        log.error("Merge between FactSet mapping and NAICS scores resulted in an empty DataFrame. Aborting.")
        return

    log.info("Successfully merged NAICS scores with FactSet mapping.")

    # --- 4. Group by FactSet Subgroup and Aggregate (Average) the Scores ---
    
    # Define the columns we want to average
    score_columns_to_average = [
         'economic_value_add_score', 'trade_imbalance_score', 
        'employment_growth_score', 'industrial_output_and_capacity_utilization_score', 'us_manufacturing_momentum_score']
    
    # Define the grouping keys
    grouping_keys = ['factset_code', 'factset_subgroup_title']
    
    # Perform the groupby and aggregation
    factset_scores_df = merged_for_rollup.groupby(grouping_keys)[score_columns_to_average].mean().reset_index()
    

    # --- 5. Clean, Add NAICS Details, and Save the Final Report ---
    factset_scores_df = factset_scores_df.round(4)

    merged_for_rollup['naics_details'] = merged_for_rollup['naics_code'] + ": " + merged_for_rollup['naics_title']

    factset_naics_details = merged_for_rollup.groupby(grouping_keys)['naics_details'].apply(lambda x: ', '.join(x))
    
    factset_scores_df = pd.merge(
        factset_scores_df,
        factset_naics_details,
        on=grouping_keys,
        how='left'
    )

    log.info(f"Aggregated scores for {len(factset_scores_df)} FactSet Subgroups.")

    # Generate a timestamp for the filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"factset_us_manufacturing_momentum_score_{timestamp}.xlsx"
    output_path = config.FINAL_REPORTS_DIR / output_filename

    factset_scores_df.to_excel(output_path, index=False)
    log.info(f"FactSet roll-up report created successfully and saved to {output_path}")


# ==============================================================================
# === MAIN EXECUTION BLOCK =============================================
# ==============================================================================

if __name__ == '__main__':
    # Uncomment the below code to run for calculating each score.
    log.info("===== Starting Data Processing and Scoring Pipeline =====")
    
    latest_quarter_from_gdp = calculate_economic_value_add_score()
    # calculate_trade_imbalance_score()
    # calculate_employment_growth_score()
    # calculate_industrial_output_score()
    # calculate_capacity_utilization_score()
    
        
    log.info("===== Data Processing and Scoring Pipeline Finished =====")
    # df = create_final_report()
    # create_factset_rollup_report(df)