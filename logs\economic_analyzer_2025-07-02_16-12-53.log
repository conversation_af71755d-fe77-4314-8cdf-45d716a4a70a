2025-07-02 16:12:53,187 - INFO - [logger.py:52] - <PERSON><PERSON> initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-02_16-12-53.log
2025-07-02 16:12:53,187 - INFO - [fetch_dataweb.py:45] - Starting DataWeb Domestic Exports fetch for years: [2023, 2024, 2025]
2025-07-02 16:12:53,187 - INFO - [fetch_dataweb.py:54] - Successfully constructed the API query.
2025-07-02 16:12:53,187 - INFO - [fetch_dataweb.py:66] - Sending request to DataWeb API...
2025-07-02 16:12:58,890 - INFO - [fetch_dataweb.py:74] - API request successful (Status 200).
2025-07-02 16:12:58,901 - INFO - [fetch_dataweb.py:85] - Successfully parsed 324 rows from API response.
2025-07-02 16:12:58,933 - INFO - [fetch_dataweb.py:110] - Transformed data into tidy format with 3888 valid monthly data points.
2025-07-02 16:12:58,942 - INFO - [fetch_dataweb.py:120] - Found existing export data. Merging and deduplicating.
2025-07-02 16:12:59,009 - INFO - [fetch_dataweb.py:133] - DataWeb Domestic Exports fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_exports_raw.csv
