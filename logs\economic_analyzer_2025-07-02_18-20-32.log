2025-07-02 18:20:32,626 - INFO - [logger.py:52] - <PERSON><PERSON> initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-02_18-20-32.log
2025-07-02 18:20:32,626 - INFO - [fetch_dataweb2.py:50] - --- Starting DataWeb Domestic Exports fetch for years: [2023, 2024, 2025] ---
2025-07-02 18:20:38,554 - INFO - [fetch_dataweb2.py:88] - Successfully processed 3888 data points for Domestic Exports.
2025-07-02 18:20:38,635 - INFO - [fetch_dataweb2.py:109] - DataWeb Domestic Exports fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_exports_raw.csv
2025-07-02 18:20:38,635 - INFO - [fetch_dataweb2.py:50] - --- Starting DataWeb Imports for Consumption fetch for years: [2023, 2024, 2025] ---
2025-07-02 18:20:42,942 - INFO - [fetch_dataweb2.py:88] - Successfully processed 3924 data points for Imports for Consumption.
2025-07-02 18:20:42,973 - INFO - [fetch_dataweb2.py:109] - DataWeb Imports for Consumption fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_imports_raw.csv
