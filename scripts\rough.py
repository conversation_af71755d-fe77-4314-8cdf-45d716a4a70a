# In scripts/process_and_score.py

def calculate_employment_growth_score(emp_weight=0.6, growth_weight=0.4):
    """
    Calculates the Employment Growth Score based on the latest available 24 months
    of BLS data. This function is self-anchoring.

    Args:
        emp_weight (float): The weight for the normalized recent employment size.
        growth_weight (float): The weight for the normalized employment growth.
    """
    log.info("--- Starting Employment Growth Score Calculation ---")

    # --- 1. Load Data ---
    try:
        bls_df = pd.read_csv(config.RAW_DATA_DIR / "bls_employment_data_raw.csv")
        master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH) # Still needed for descriptions
        log.info("Successfully loaded raw BLS employment data and the master sheet.")
    except FileNotFoundError as e:
        log.error(f"Could not find required data file. Error: {e}. Aborting.")
        return

    # --- 2. Determine 24-Month Analysis Window (NEW SELF-ANCHORING LOGIC) ---
    
    # First, create a proper date column in the BLS DataFrame
    bls_df['month'] = bls_df['period'].str.replace('M', '').astype(int)
    bls_df['date'] = pd.to_datetime(bls_df['year'].astype(str) + '-' + bls_df['month'].astype(str) + '-01').dt.date
    
    # Find the latest month that is present for ALL series in the dataset
    # This ensures our data is complete for the anchor month.
    latest_month = bls_df.groupby('seriesID')['date'].max().min()
    
    if pd.isna(latest_month):
        log.error("No valid dates found in the BLS data. Aborting score calculation.")
        return
        
    log.info(f"Latest complete month found in BLS data: {latest_month}. Using this as the anchor.")
    
    # Generate a list of the last 24 months
    end_date_of_window = pd.to_datetime(latest_month)
    all_24m_dates = [(end_date_of_window - pd.DateOffset(months=i)).date() for i in range(24)]
    
    # Split the list into two 12-month periods
    recent_12m_dates = all_24m_dates[:12]
    previous_12m_dates = all_24m_dates[12:]
    
    log.info(f"Recent 12-month window: {min(recent_12m_dates)} to {max(recent_12m_dates)}")
    log.info(f"Previous 12-month window: {min(previous_12m_dates)} to {max(previous_12m_dates)}")

    # --- 3. Filter Data ---
    # (The `merged_bls_df` logic is removed as we operate directly on bls_df now)
    recent_emp_df = bls_df[bls_df['date'].isin(recent_12m_dates)]
    previous_emp_df = bls_df[bls_df['date'].isin(previous_12m_dates)]

    # --- 4. Aggregate Data ---
    # ... (The rest of the function remains exactly the same) ...
    recent_emp_avg = recent_emp_df.groupby('seriesID')['value'].mean()
    previous_emp_avg = previous_emp_df.groupby('seriesID')['value'].mean()
    
    emp_totals_df = pd.DataFrame({'Recent_Emp_12M_Avg': recent_emp_avg, 'Previous_Emp_12M_Avg': previous_emp_avg})
    emp_totals_df.dropna(inplace=True) # Drop series not present in both periods

    # (The rest of the calculation, normalization, and saving logic is unchanged)