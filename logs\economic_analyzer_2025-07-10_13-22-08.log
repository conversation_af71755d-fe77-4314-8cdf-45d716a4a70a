2025-07-10 13:22:08,040 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-10_13-22-08.log
2025-07-10 13:22:08,040 - INFO - [process_and_score.py:560] - ===== Starting Data Processing and Scoring Pipeline =====
2025-07-10 13:22:08,040 - INFO - [process_and_score.py:28] - --- Starting Economic Value Add Score Calculation ---
2025-07-10 13:22:08,058 - INFO - [process_and_score.py:36] - Successfully loaded raw BEA GDP data and the master sheet.
2025-07-10 13:22:08,078 - INFO - [process_and_score.py:54] - Latest complete quarter available for all industries: 2025-Q1
2025-07-10 13:22:08,091 - INFO - [process_and_score.py:72] - Recent 4 Quarters for analysis: ['2025-Q1', '2024-Q4', '2024-Q3', '2024-Q2']
2025-07-10 13:22:08,092 - INFO - [process_and_score.py:73] - Previous 4 Quarters for analysis: ['2024-Q1', '2023-Q4', '2023-Q3', '2023-Q2']
2025-07-10 13:22:08,128 - INFO - [process_and_score.py:98] - Calculated and normalized GDP size and growth scores.
2025-07-10 13:22:08,916 - INFO - [process_and_score.py:134] - Economic Value Add Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\economic_value_add_score.xlsx
2025-07-10 13:22:08,918 - INFO - [process_and_score.py:264] - --- Starting Employment Growth Score Calculation ---
2025-07-10 13:22:08,952 - INFO - [process_and_score.py:269] - Successfully loaded raw BLS employment data and the master sheet.
2025-07-10 13:22:08,953 - INFO - [process_and_score.py:275] - Aligning monthly employment data with latest GDP quarter: 2025-Q1
2025-07-10 13:22:08,964 - INFO - [process_and_score.py:286] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-10 13:22:08,964 - INFO - [process_and_score.py:287] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-10 13:22:09,005 - INFO - [process_and_score.py:329] - Calculated and normalized employment size and growth scores.
2025-07-10 13:22:09,132 - INFO - [process_and_score.py:344] - Employment Growth Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\employment_growth_score.xlsx
2025-07-10 13:22:09,132 - INFO - [process_and_score.py:360] - --- Starting YoY Score Calculation for FRED Indicator: Industry_Production ---
2025-07-10 13:22:09,201 - INFO - [process_and_score.py:365] - Successfully loaded raw FRED data.
2025-07-10 13:22:09,222 - INFO - [process_and_score.py:375] - Using latest month: 2025-03-01 and previous year's month: 2024-03-01
2025-07-10 13:22:09,251 - INFO - [process_and_score.py:405] - Calculated and normalized YoY growth for 79 series.
2025-07-10 13:22:09,321 - INFO - [process_and_score.py:424] - Indutsrial Output Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\industrial_output_score.xlsx
2025-07-10 13:22:09,321 - INFO - [process_and_score.py:360] - --- Starting YoY Score Calculation for FRED Indicator: Capacity_Utilization ---
2025-07-10 13:22:09,340 - INFO - [process_and_score.py:365] - Successfully loaded raw FRED data.
2025-07-10 13:22:09,343 - INFO - [process_and_score.py:375] - Using latest month: 2025-03-01 and previous year's month: 2024-03-01
2025-07-10 13:22:09,360 - INFO - [process_and_score.py:405] - Calculated and normalized YoY growth for 26 series.
2025-07-10 13:22:09,423 - INFO - [process_and_score.py:438] - Capacity Utilization Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\capacity_utilization_score.xlsx
2025-07-10 13:22:09,429 - INFO - [process_and_score.py:572] - ===== Data Processing and Scoring Pipeline Finished =====
