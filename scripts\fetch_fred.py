import pandas as pd
import sys
import time
from pathlib import Path
from fredapi import Fred

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config
from logger import log

def fetch_and_save_fred_data(start_date: str, end_date: str):
    """
    Fetches monthly FRED data for a specified date range.

    Args:
    start_date (str): The start date for the data pull in 'YYYY-MM-DD' format.
    end_date (str): The end date for the data pull in 'YYYY-MM-DD' format.
    """
    log.info(f"Starting FRED data fetch process for  {start_date} to {end_date}.")

    try:
        master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH)
    except FileNotFoundError:
        log.error(f"Master sheet not found at {config.MASTER_SHEET_PATH}. Aborting FRED fetch.")
        return

    indicator_mapping = {
        'SeriesID_Industry_Production(M)': 'Industry_Production',
        'SeriesID_Capacity_Utilization(M)': 'Capacity_Utilization'
    }

    try:
        fred = Fred(api_key=config.FRED_API_KEY)
    except Exception as e:
        log.error(f"Failed to initialize FRED API client. Error: {e}")
        return

    all_fred_data = []
    
    for column_name, indicator_name in indicator_mapping.items():

        series_ids = master_sheet_df[column_name].dropna().unique().tolist()
        if not series_ids:
            log.info(f"No series IDs for indicator '{indicator_name}'. Skipping.")
            continue

        log.info(f"Fetching {len(series_ids)} series for indicator: '{indicator_name}'...")

        for series_id in series_ids:
            try:
                # Fetch data for the specified date range
                data = fred.get_series(series_id, observation_start=start_date, observation_end = end_date)
                
                if data.empty:
                    log.warning(f"No data returned for series ID: {series_id}")
                    continue

                df = data.reset_index()
                df.columns = ['date', 'value']
                df['series_id'] = series_id
                df['indicator'] = indicator_name
                all_fred_data.append(df)
                
                time.sleep(0.5)

            except Exception as e:
                log.error(f"Failed to fetch data for series ID '{series_id}'. Error: {e}")

    # Concatenation and deduplication
    if not all_fred_data:
        log.warning("No data was fetched from FRED API. Exiting.")
        return

    new_df = pd.concat(all_fred_data, ignore_index=True)
    new_df['date'] = pd.to_datetime(new_df['date']).dt.date
    new_df['series_id'] = new_df['series_id'].astype(str)
    new_df['indicator'] = new_df['indicator'].astype(str)
    log.info(f"Fetched {len(new_df)} total data points from FRED.")

    output_path = config.RAW_DATA_DIR / "fred_data_raw.csv"
    if output_path.exists():
        log.info("Merging with existing data and deduplicating.")
        old_df = pd.read_csv(output_path)
        old_df['date'] = pd.to_datetime(old_df['date']).dt.date
        old_df['series_id'] = old_df['series_id'].astype(str)
        old_df['indicator'] = old_df['indicator'].astype(str)
        
        combined_df = pd.concat([old_df, new_df], ignore_index=True)
        final_df = combined_df.drop_duplicates(subset=['date', 'series_id', 'indicator'], keep='last')
    else:
        final_df = new_df

    final_df = final_df.sort_values(by=['indicator', 'series_id', 'date']).reset_index(drop=True)
    final_df.to_csv(output_path, index=False)
    log.info(f"FRED data fetch complete. Data saved to {output_path}")


if __name__ == '__main__':
    fetch_and_save_fred_data(start_date="2023-01-01", end_date="2025-07-08")
