2025-07-07 19:26:17,812 - INFO - [logger.py:52] - <PERSON><PERSON> initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-07_19-26-17.log
2025-07-07 19:26:17,812 - INFO - [fetch_dataweb.py:50] - --- Starting DataWeb Domestic Exports fetch for years: [2023, 2024, 2025] ---
2025-07-07 19:26:17,828 - INFO - [fetch_dataweb.py:56] - Found 86 unique NAICS codes in master sheet to filter for.
2025-07-07 19:26:20,478 - ERROR - [fetch_dataweb.py:76] - Failed to get data for Domestic Exports. Error: 429 Client Error: Too Many Requests for url: https://datawebws.usitc.gov/dataweb/api/v2/report2/runReport
2025-07-07 19:26:20,494 - INFO - [fetch_dataweb.py:50] - --- Starting DataWeb Imports for Consumption fetch for years: [2023, 2024, 2025] ---
2025-07-07 19:26:20,494 - INFO - [fetch_dataweb.py:56] - Found 86 unique NAICS codes in master sheet to filter for.
2025-07-07 19:26:24,259 - INFO - [fetch_dataweb.py:98] - Successfully processed 3924 data points for Imports for Consumption.
2025-07-07 19:26:24,259 - INFO - [fetch_dataweb.py:100] - Filtering Imports for Consumption data based on master sheet NAICS codes...
2025-07-07 19:26:24,274 - INFO - [fetch_dataweb.py:106] - Filtered 3924 rows down to 3060 relevant NAICS rows.
2025-07-07 19:26:24,300 - INFO - [fetch_dataweb.py:127] - DataWeb Imports for Consumption fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_imports_raw.csv
