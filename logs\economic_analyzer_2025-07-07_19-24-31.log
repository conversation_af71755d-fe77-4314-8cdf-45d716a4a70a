2025-07-07 19:24:31,917 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-07_19-24-31.log
2025-07-07 19:24:31,917 - INFO - [fetch_bea.py:22] - Starting BEA GDP by Industry data fetch for years: [2023, 2024, 2025]
2025-07-07 19:24:31,933 - INFO - [fetch_bea.py:38] - Found 19 unique BEA industry codes to filter for.
2025-07-07 19:24:31,933 - INFO - [fetch_bea.py:44] - Requesting data from BEA API...
2025-07-07 19:24:35,431 - INFO - [fetch_bea.py:53] - Successfully received data from BEA. Total rows returned: 891
2025-07-07 19:24:35,446 - INFO - [fetch_bea.py:63] - Filtered 891 rows down to 171 relevant industry rows.
2025-07-07 19:24:35,454 - INFO - [fetch_bea.py:91] - Found existing BEA data at C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bea_gdp_by_industry_raw.csv. Merging and deduplicating.
2025-07-07 19:24:35,465 - INFO - [fetch_bea.py:98] - Combined BEA data size after deduplication: 171 rows.
2025-07-07 19:24:35,472 - INFO - [fetch_bea.py:102] - BEA data fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bea_gdp_by_industry_raw.csv
