#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Apr 27 21:04:41 2025

@author: kamal
"""



import pandas as pd


#Import Intensity

# Load your Excel file (replace 'your_file.xlsx' with your actual filename)
file_path = 'Dataweb-Deficit.xlsx'  # Adjust path if needed
sheet_name = 'Exports'  # Adjust if necessary

# Read the data
df = pd.read_excel(file_path, sheet_name=sheet_name)


# Filter for NAICS codes starting with '3'
df = df[df['NAICS'].astype(str).str.startswith('3')]

# Calculate Import Intensity Score based on both imports and trade balance
# Normalize Imports and Trade Deficit separately, then combine

df['Imports_Score'] = (df['Imports'] - df['Imports'].min()) / (df['Imports'].max() - df['Imports'].min())


df['Deficit_Score'] = ((df['Trade Balance']) - (df['Trade Balance']).min()) / ((df['Trade Balance']).max() - (df['Trade Balance']).min())

# Combine both with weights (you can adjust)
import_weight = 0.5
deficit_weight = 0.5

df['Import_Intensity_Score'] = (import_weight * df['Imports_Score'] + deficit_weight * df['Deficit_Score'])

# Sort descending by score
df = df.sort_values(by='Import_Intensity_Score', ascending=False)

# Save output
output_path = 'NAICS_Import_Intensity_Score.xlsx'
df.to_excel(output_path, index=False)




#GDP

# Load your Excel file (replace 'your_file_gdp.xlsx' with your actual filename)
file_path_gdp = 'growth_rates_output.xlsx'  # Adjust path for GDP file
sheet_name_gdp = 'Sheet1'  # Adjust if necessary

# Read the data
df_gdp = pd.read_excel(file_path_gdp, sheet_name=sheet_name_gdp)


# Filter for NAICS codes starting with '3'
# Filter for NAICS codes starting with '3' and whose first 3 characters are all digits
df_gdp = df_gdp[df_gdp['Industry'].astype(str).str.match(r'^3\d{2}') & df_gdp['Industry'].astype(str).str[:3].str.isdigit()]



# Calculate Economic Heft and Growth Score
# Normalize GDP and GDP Growth Rate separately, then combine

df_gdp['GDP_Score'] = (df_gdp['GDP_2024'] - df_gdp['GDP_2024'].min()) / (df_gdp['GDP_2024'].max() - df_gdp['GDP_2024'].min())

df_gdp['GDP_Growth_Score'] = (df_gdp['Recent_Growth_%'] - df_gdp['Recent_Growth_%'].min()) / (df_gdp['Recent_Growth_%'].max() - df_gdp['Recent_Growth_%'].min())

# Assign weights to GDP size and GDP growth
gdp_weight = 0.6
growth_weight = 0.4

df_gdp['Economic_Heft_Score'] = (gdp_weight * df_gdp['GDP_Score'] + growth_weight * df_gdp['GDP_Growth_Score'])

# Sort descending by Economic Heft Score
df_gdp = df_gdp.sort_values(by='Economic_Heft_Score', ascending=False)

# Save output
output_path = 'NAICS_Economic_Heft_Scores.xlsx'
df_gdp.to_excel(output_path, index=False)


#GDP Underlying

# Load your Excel file (replace 'your_file_gdp.xlsx' with your actual filename)
file_path_gdp = 'growth_rates_output_underlying.xlsx'  # Adjust path for GDP file
sheet_name_gdp = 'Sheet1'  # Adjust if necessary

# Read the data
df_gdpu = pd.read_excel(file_path_gdp, sheet_name=sheet_name_gdp)


# Filter for NAICS codes starting with '3'
# Filter for NAICS codes starting with '3' and whose first 3 characters are all digits
df_gdpu = df_gdpu[df_gdpu['Industry'].astype(str).str.startswith('3')  & df_gdpu['Industry'].astype(str).str[:3].str.isdigit()]



# Calculate Economic Heft and Growth Score
# Normalize GDP and GDP Growth Rate separately, then combine

df_gdpu['GDP_Score'] = (df_gdpu['GDP_2023'] - df_gdpu['GDP_2023'].min()) / (df_gdpu['GDP_2023'].max() - df_gdpu['GDP_2023'].min())

df_gdpu['GDP_Growth_Score'] = (df_gdpu['Recent_Growth_%'] - df_gdpu['Recent_Growth_%'].min()) / (df_gdpu['Recent_Growth_%'].max() - df_gdpu['Recent_Growth_%'].min())

# Assign weights to GDP size and GDP growth
gdp_weight = 0.6
growth_weight = 0.4

df_gdpu['Economic_Heft_Score'] = (gdp_weight * df_gdpu['GDP_Score'] + growth_weight * df_gdpu['GDP_Growth_Score'])

# Sort descending by Economic Heft Score
df_gdpu = df_gdpu.sort_values(by='Economic_Heft_Score', ascending=False)

# Save output
output_path = 'NAICS_Economic_Heft_Scores_underlying.xlsx'
df_gdpu.to_excel(output_path, index=False)


#Fixed Assets

# Load your Excel file (replace 'your_file_gdp.xlsx' with your actual filename)
file_path_gdp = 'growth_rates_output_fixed_assets.xlsx'  # Adjust path for GDP file
sheet_name_gdp = 'Sheet1'  # Adjust if necessary

# Read the data
df_fa = pd.read_excel(file_path_gdp, sheet_name=sheet_name_gdp)


# Filter for NAICS codes starting with '3'
# Filter for NAICS codes starting with '3' and whose first 3 characters are all digits
df_fa = df_fa[df_fa['Industry'].astype(str).str.startswith('3') & df_fa['Industry'].astype(str).str[:3].str.isdigit()]


# Calculate Economic Heft and Growth Score
# Normalize GDP and GDP Growth Rate separately, then combine

df_fa['FA_Score'] = (df_fa['FA_2023'] - df_fa['FA_2023'].min()) / (df_fa['FA_2023'].max() - df_fa['FA_2023'].min())

df_fa['FA_Growth_Score'] = (df_fa['Recent_Growth_%'] - df_fa['Recent_Growth_%'].min()) / (df_fa['Recent_Growth_%'].max() - df_fa['Recent_Growth_%'].min())

# Assign weights to GDP size and GDP growth
gdp_weight = 0.6
growth_weight = 0.4

df_fa['Fixed_Assets_Score'] = (gdp_weight * df_fa['FA_Score'] + growth_weight * df_fa['FA_Growth_Score'])

# Sort descending by Economic Heft Score
df_fa = df_fa.sort_values(by='Fixed_Assets_Score', ascending=False)

# Save output
output_path = 'NAICS_Fixed_Assets_Scores.xlsx'
df_fa.to_excel(output_path, index=False)


#Employment

# Load your Excel file (replace 'your_file_gdp.xlsx' with your actual filename)
file_path_gdp = 'manufacturing_employment_rankings.xlsx'  # Adjust path for GDP file
sheet_name_gdp = 'Sheet1'  # Adjust if necessary

# Read the data
df_emp = pd.read_excel(file_path_gdp, sheet_name=sheet_name_gdp)


# Filter for NAICS codes starting with '3'
# Filter for NAICS codes starting with '3' and whose first 3 characters are all digits
df_emp = df_emp[df_emp['Industry'].astype(str).str.startswith('3') & df_emp['Industry'].astype(str).str[:3].str.isdigit()]


# Calculate Economic Heft and Growth Score
# Normalize GDP and GDP Growth Rate separately, then combine

df_emp['Emp_Score'] = (df_emp['avg_2024'] - df_emp['avg_2024'].min()) / (df_emp['avg_2024'].max() - df_emp['avg_2024'].min())

df_emp['Emp_Growth_Score'] = (df_emp['Recent_Growth_%'] - df_emp['Recent_Growth_%'].min()) / (df_emp['Recent_Growth_%'].max() - df_emp['Recent_Growth_%'].min())

# Assign weights to GDP size and GDP growth
scale_weight = 0.6
growth_weight = 0.4

df_emp['Employment_Score'] = (scale_weight * df_emp['Emp_Score'] + growth_weight * df_emp['Emp_Growth_Score'])

# Sort descending by Economic Heft Score
df_emp = df_emp.sort_values(by='Employment_Score', ascending=False)

# Save output
output_path = 'NAICS_Employment_Scores.xlsx'
df_emp.to_excel(output_path, index=False)
