from pathlib import Path
# ------ API KEYS ------
DATAWEB_TOKEN = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDAwOTg5IiwianRpIjoiMjY0MGQxMDMtMmM2MS00MDJkLTg2NTQtMzFiODY1YzExYWZhIiwiaXNzIjoiZGF0YXdlYiIsImlhdCI6MTc1MTg5NjY4NCwiZXhwIjoxNzUzMTA2Mjg0fQ.975rg3PYIn4-JIejXqB3-yR-Ht5OiZ9Jy0wfSi2j487v2odBfhJA50govuUkRwVs-3cfC0lIECpEzEdks52OXw"
BEA_API_KEY = "0CB6FB71-8009-4FF4-B57F-184021F1A352"
FRED_API_KEY = "51c0e7ec1da836c20ce6d40a9e50f5d1"
BLS_API_KEY = "77631563995c4764bcfe327957cb19cf"

PROJECT_ROOT = Path(__file__).resolve().parent
#----- Master sheet path-----
MASTER_SHEET_PATH = PROJECT_ROOT / "master_sheet.csv"

#-----FACTSET TO NAICS MAPPING SHEET-----
FACTSET_MAPPING_PATH = PROJECT_ROOT / "FACTSET_TO_NAICS.xlsx"

# --- DATA FOLDER PATHS ---
RAW_DATA_DIR = PROJECT_ROOT / "data" / "raw"
PROCESSED_DATA_DIR = PROJECT_ROOT / "data" / "processed"
FINAL_REPORTS_DIR = PROJECT_ROOT / "data" / "final_reports"
LOGS_DIR = PROJECT_ROOT / "logs"

#---Create directories if they do not exist----
RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)
FINAL_REPORTS_DIR.mkdir(parents=True, exist_ok=True)
LOGS_DIR.mkdir(parents=True, exist_ok=True)
