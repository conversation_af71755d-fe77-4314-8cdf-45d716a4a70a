2025-07-10 14:57:04,995 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-10_14-57-04.log
2025-07-10 14:57:04,995 - INFO - [process_and_score.py:450] - --- Starting Final Report Combination ---
2025-07-10 14:57:05,006 - INFO - [process_and_score.py:455] - Loaded master sheet with 86 rows.
2025-07-10 14:57:05,701 - INFO - [process_and_score.py:462] - Successfully loaded all individual score files.
2025-07-10 14:57:05,701 - INFO - [process_and_score.py:505] - Merging scores into the final report...
2025-07-10 14:57:05,722 - INFO - [process_and_score.py:513] - All scores have been merged.
2025-07-10 14:57:05,722 - INFO - [process_and_score.py:516] - Calculating Industrial output and capacity utilization score and the final overall score...
2025-07-10 14:57:05,734 - INFO - [process_and_score.py:522] - Calculating US Manufacturing Momentum Score...
2025-07-10 14:57:05,852 - INFO - [process_and_score.py:547] - Internal Final report created successfully and saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\final_reports\internal_us_manufacturing_momentum_score_20250710_145705.xlsx
2025-07-10 14:57:05,852 - INFO - [process_and_score.py:550] - Cleaning up final report columns...
2025-07-10 14:57:05,904 - INFO - [process_and_score.py:570] - Final report created successfully and saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\final_reports\us_manufacturing_momentum_score_20250710_145705.xlsx
