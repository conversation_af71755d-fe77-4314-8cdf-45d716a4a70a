2025-07-02 09:53:59,413 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-02_09-53-59.log
2025-07-02 09:53:59,429 - INFO - [fetch_bea.py:22] - Starting BEA GDP by Industry data fetch for years: [2023, 2024, 2025]
2025-07-02 09:53:59,494 - INFO - [fetch_bea.py:38] - Found 30 unique BEA industry codes to filter for.
2025-07-02 09:53:59,494 - INFO - [fetch_bea.py:44] - Requesting data from BEA API...
2025-07-02 09:54:05,142 - INFO - [fetch_bea.py:53] - Successfully received data from BEA. Total rows returned: 900
2025-07-02 09:54:05,158 - INFO - [fetch_bea.py:63] - Filtered 900 rows down to 126 relevant industry rows.
2025-07-02 09:54:05,193 - INFO - [fetch_bea.py:102] - BEA data fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bea_gdp_by_industry_raw.csv
