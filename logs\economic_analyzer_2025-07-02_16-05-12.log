2025-07-02 16:05:12,250 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-02_16-05-12.log
2025-07-02 16:05:12,250 - INFO - [fetch_dataweb.py:45] - Starting DataWeb Domestic Exports fetch for years: [2023, 2024, 2025]
2025-07-02 16:05:12,250 - INFO - [fetch_dataweb.py:54] - Successfully constructed the API query.
2025-07-02 16:05:12,250 - INFO - [fetch_dataweb.py:66] - Sending request to DataWeb API...
2025-07-02 16:05:16,963 - INFO - [fetch_dataweb.py:74] - API request successful (Status 200).
2025-07-02 16:05:16,966 - INFO - [fetch_dataweb.py:85] - Successfully parsed 324 rows from API response.
2025-07-02 16:05:17,115 - INFO - [fetch_dataweb.py:110] - Transformed data into tidy format with 3888 valid monthly data points.
2025-07-02 16:05:17,216 - INFO - [fetch_dataweb.py:133] - DataWeb Domestic Exports fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_exports_raw.csv
