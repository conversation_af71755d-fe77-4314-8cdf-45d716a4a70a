import requests
import json
import pandas as pd
import sys
import time
from pathlib import Path

project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

import config
from logger import log

MAX_SERIES_PER_REQUEST = 25
BLS_API_ENDPOINT = 'https://api.bls.gov/publicAPI/v2/timeseries/data/' 

def fetch_and_save_bls_data(series_ids: list, start_year: str, end_year: str):
    """
    Fetches data from the BLS API, handles deduplication with existing data,
    and saves it to a single CSV file.

    Args:
        series_ids (list): A list of BLS series ID strings.
        start_year (str): The starting year for the data pull.
        end_year (str): The ending year for the data pull.
    """
    log.info("Starting BLS data fetch process.")
    
    all_series_data = []
    
    # 1. Chunk the series_ids into lists of appropriate size for the API
    id_chunks = [series_ids[i:i + MAX_SERIES_PER_REQUEST] for i in range(0, len(series_ids), MAX_SERIES_PER_REQUEST)]
    
    log.info(f"Total unique series to fetch: {len(series_ids)}. This will require {len(id_chunks)} API calls.")

    # 2. Loop through the chunks, making an API call for each
    for i, chunk in enumerate(id_chunks):
        log.info(f"Fetching chunk {i+1}/{len(id_chunks)}...")
        headers = {'Content-type': 'application/json'}
        data = json.dumps({
            "seriesid": chunk,
            "startyear": start_year,
            "endyear": end_year,
            "registrationkey": config.BLS_API_KEY 
        })

        try:
            p = requests.post(BLS_API_ENDPOINT, data=data, headers=headers)
            p.raise_for_status()  # This will raise an HTTPError if the status is 4xx or 5xx
            json_data = p.json()

            if json_data['status'] != 'REQUEST_SUCCEEDED':
                log.warning(f"API request for chunk {i+1} was not successful. Message: {json_data.get('message')}")
                continue

            # 3. Collect all the results into a single list
            for series in json_data['Results']['series']:
                series_id = series['seriesID']
                for item in series['data']:
                    all_series_data.append({
                        'seriesID': series_id,
                        'year': item['year'],
                        'period': item['period'],
                        'periodName': item['periodName'],
                        'value': item['value']
                    })
            
            # The BLS API has rate limits, a small delay can prevent issues.
            time.sleep(1) 

        except requests.exceptions.RequestException as e:
            log.error(f"An error occurred during API request for chunk {i+1}: {e}")
        except json.JSONDecodeError:
            log.error(f"Failed to decode JSON response for chunk {i+1}. Response text: {p.text}")
            
    if not all_series_data:
        log.warning("No new data was fetched from the API. Exiting BLS fetch process.")
        return

    # 4. Convert the list of new data to a pandas DataFrame
    new_df = pd.DataFrame(all_series_data)
    log.info(f"Successfully fetched {len(new_df)} data points from the API.")
    new_df['year'] = pd.to_numeric(new_df['year'])
    new_df['seriesID'] = new_df['seriesID'].astype(str)
    new_df['period'] = new_df['period'].astype(str)

    # --- THIS IS THE NEW DEDUPLICATION LOGIC ---
    output_path = config.RAW_DATA_DIR / "bls_employment_data_raw.csv"

    final_df = new_df

    if output_path.exists():
        log.info(f"Found existing data at {output_path}. Merging and deduplicating.")
        old_df = pd.read_csv(output_path)
        old_df['year'] = pd.to_numeric(old_df['year'])
        old_df['seriesID'] = old_df['seriesID'].astype(str)
        old_df['period'] = old_df['period'].astype(str)
        
        combined_df = pd.concat([old_df, new_df], ignore_index=True)
        
        # Drop duplicates based on the unique keys, keeping the *last* entry (the new one)
        final_df = combined_df.drop_duplicates(subset=['seriesID', 'year', 'period'], keep='last')
        log.info(f"Combined data size after deduplication: {len(final_df)} rows.")
    
    # Sort the data for consistency before saving
    final_df = final_df.sort_values(by=['seriesID', 'year', 'period'], ascending=[True, False, False])

    # 5. Save the final, clean DataFrame to the raw_data directory
    final_df.to_csv(output_path, index=False)
    log.info(f"BLS data fetch complete. Data saved to {output_path}")


if __name__ == '__main__':
    
    
    master_sheet_df = pd.read_csv(config.MASTER_SHEET_PATH)
    series_to_fetch = master_sheet_df['seasonal_adj_bls_series_id'].dropna().unique().tolist()
    
    fetch_and_save_bls_data(series_ids=series_to_fetch, start_year="2023", end_year="2025")