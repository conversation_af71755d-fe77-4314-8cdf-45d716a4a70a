2025-07-07 19:28:50,048 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-07_19-28-50.log
2025-07-07 19:28:50,048 - INFO - [fetch_dataweb.py:50] - --- Starting DataWeb Domestic Exports fetch for years: [2023, 2024, 2025] ---
2025-07-07 19:28:50,071 - INFO - [fetch_dataweb.py:56] - Found 86 unique NAICS codes in master sheet to filter for.
2025-07-07 19:28:58,996 - INFO - [fetch_dataweb.py:98] - Successfully processed 3888 data points for Domestic Exports.
2025-07-07 19:28:58,996 - INFO - [fetch_dataweb.py:100] - Filtering Domestic Exports data based on master sheet NAICS codes...
2025-07-07 19:28:59,010 - INFO - [fetch_dataweb.py:106] - Filtered 3888 rows down to 3060 relevant NAICS rows.
2025-07-07 19:28:59,038 - INFO - [fetch_dataweb.py:127] - DataWeb Domestic Exports fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\dataweb_exports_raw.csv
