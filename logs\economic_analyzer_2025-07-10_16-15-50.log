2025-07-10 16:15:50,765 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\logs\economic_analyzer_2025-07-10_16-15-50.log
2025-07-10 16:15:50,768 - INFO - [process_and_score.py:596] - ===== Starting Data Processing and Scoring Pipeline =====
2025-07-10 16:15:50,769 - INFO - [process_and_score.py:66] - --- Starting Economic Value Add Score Calculation ---
2025-07-10 16:15:50,814 - INFO - [process_and_score.py:74] - Successfully loaded raw BEA GDP data and the master sheet.
2025-07-10 16:15:50,893 - INFO - [process_and_score.py:92] - Latest complete quarter available for all industries: 2025-Q1
2025-07-10 16:15:50,904 - INFO - [process_and_score.py:110] - Recent 4 Quarters for analysis: ['2025-Q1', '2024-Q4', '2024-Q3', '2024-Q2']
2025-07-10 16:15:50,905 - INFO - [process_and_score.py:111] - Previous 4 Quarters for analysis: ['2024-Q1', '2023-Q4', '2023-Q3', '2023-Q2']
2025-07-10 16:15:50,956 - INFO - [process_and_score.py:131] - Calculated and normalized GDP size and growth scores.
2025-07-10 16:15:52,338 - INFO - [process_and_score.py:167] - Economic Value Add Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\economic_value_add_score.xlsx
2025-07-10 16:15:52,344 - INFO - [process_and_score.py:287] - --- Starting Employment Growth Score Calculation ---
2025-07-10 16:15:52,370 - INFO - [process_and_score.py:292] - Successfully loaded raw BLS employment data and the master sheet.
2025-07-10 16:15:52,377 - INFO - [process_and_score.py:298] - Aligning monthly employment data with latest GDP quarter: 2025-Q1
2025-07-10 16:15:52,429 - INFO - [process_and_score.py:309] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-10 16:15:52,437 - INFO - [process_and_score.py:310] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-10 16:15:52,501 - INFO - [process_and_score.py:348] - Calculated and normalized employment size and growth scores.
2025-07-10 16:15:52,645 - INFO - [process_and_score.py:363] - Employment Growth Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\employment_growth_score.xlsx
2025-07-10 16:15:52,647 - INFO - [process_and_score.py:379] - --- Starting YoY Score Calculation for FRED Indicator: Industry_Production ---
2025-07-10 16:15:52,663 - INFO - [process_and_score.py:384] - Successfully loaded raw FRED data.
2025-07-10 16:15:52,668 - INFO - [process_and_score.py:394] - Using latest month: 2025-03-01 and previous year's month: 2024-03-01
2025-07-10 16:15:52,721 - INFO - [process_and_score.py:422] - Calculated and normalized YoY growth for 79 series.
2025-07-10 16:15:52,820 - INFO - [process_and_score.py:441] - Indutsrial Output Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\industrial_output_score.xlsx
2025-07-10 16:15:52,822 - INFO - [process_and_score.py:379] - --- Starting YoY Score Calculation for FRED Indicator: Capacity_Utilization ---
2025-07-10 16:15:52,898 - INFO - [process_and_score.py:384] - Successfully loaded raw FRED data.
2025-07-10 16:15:53,017 - INFO - [process_and_score.py:394] - Using latest month: 2025-03-01 and previous year's month: 2024-03-01
2025-07-10 16:15:53,115 - INFO - [process_and_score.py:422] - Calculated and normalized YoY growth for 26 series.
2025-07-10 16:15:53,332 - INFO - [process_and_score.py:455] - Capacity Utilization Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\capacity_utilization_score.xlsx
2025-07-10 16:15:53,345 - INFO - [process_and_score.py:608] - ===== Data Processing and Scoring Pipeline Finished =====
2025-07-10 16:15:53,347 - INFO - [process_and_score.py:467] - --- Starting Final Report Combination ---
2025-07-10 16:15:53,357 - INFO - [process_and_score.py:472] - Loaded master sheet with 86 rows.
2025-07-10 16:15:53,423 - ERROR - [process_and_score.py:482] - Could not find a required data or score file. Error: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\Autowiz\\new_proj\\sec_data\\analytical\\NAICS_FACTSET_SCORE_AUTOMATION - Copy\\data\\processed\\trade_imbalance_score.xlsx'. Aborting report creation.
