2025-07-01 23:34:00,304 - INFO - [logger.py:52] - <PERSON><PERSON> initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-01_23-34-00.log
2025-07-01 23:34:00,328 - INFO - [fetch_bls.py:27] - Starting BLS data fetch process.
2025-07-01 23:34:00,328 - INFO - [fetch_bls.py:34] - Total unique series to fetch: 76. This will require 4 API calls.
2025-07-01 23:34:00,328 - INFO - [fetch_bls.py:38] - Fetching chunk 1/4...
2025-07-01 23:34:03,030 - INFO - [fetch_bls.py:38] - Fetching chunk 2/4...
2025-07-01 23:34:06,016 - INFO - [fetch_bls.py:38] - Fetching chunk 3/4...
2025-07-01 23:34:08,587 - INFO - [fetch_bls.py:38] - Fetching chunk 4/4...
2025-07-01 23:34:10,974 - INFO - [fetch_bls.py:82] - Successfully fetched 3648 data points from the API.
2025-07-01 23:34:10,995 - INFO - [fetch_bls.py:93] - Found existing data at C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bls_employment_data_raw.csv. Merging and deduplicating.
2025-07-01 23:34:11,056 - INFO - [fetch_bls.py:103] - Combined data size after deduplication: 3648 rows.
2025-07-01 23:34:11,073 - INFO - [fetch_bls.py:110] - BLS data fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bls_employment_data_raw.csv
