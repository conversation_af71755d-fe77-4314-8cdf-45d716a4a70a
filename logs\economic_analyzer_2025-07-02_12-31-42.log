2025-07-02 12:31:42,290 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-02_12-31-42.log
2025-07-02 12:31:42,290 - INFO - [fetch_bea.py:22] - Starting BEA GDP by Industry data fetch for years: [2023, 2024, 2025]
2025-07-02 12:31:42,350 - INFO - [fetch_bea.py:38] - Found 30 unique BEA industry codes to filter for.
2025-07-02 12:31:42,350 - INFO - [fetch_bea.py:44] - Requesting data from BEA API...
2025-07-02 12:31:50,360 - INFO - [fetch_bea.py:53] - Successfully received data from BEA. Total rows returned: 900
2025-07-02 12:31:50,376 - INFO - [fetch_bea.py:63] - Filtered 900 rows down to 126 relevant industry rows.
2025-07-02 12:31:50,392 - INFO - [fetch_bea.py:91] - Found existing BEA data at C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bea_gdp_by_industry_raw.csv. Merging and deduplicating.
2025-07-02 12:31:50,411 - INFO - [fetch_bea.py:98] - Combined BEA data size after deduplication: 126 rows.
2025-07-02 12:31:50,424 - INFO - [fetch_bea.py:102] - BEA data fetch complete. Data saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\raw\bea_gdp_by_industry_raw.csv
