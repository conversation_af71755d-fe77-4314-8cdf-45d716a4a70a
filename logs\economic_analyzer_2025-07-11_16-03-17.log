2025-07-11 16:03:17,737 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\logs\economic_analyzer_2025-07-11_16-03-17.log
2025-07-11 16:03:17,737 - INFO - [process_and_score.py:689] - ===== Starting Data Processing and Scoring Pipeline =====
2025-07-11 16:03:17,737 - INFO - [process_and_score.py:66] - --- Starting Economic Value Add Score Calculation ---
2025-07-11 16:03:17,795 - INFO - [process_and_score.py:74] - Successfully loaded raw BEA GDP data and the master sheet.
2025-07-11 16:03:17,839 - INFO - [process_and_score.py:92] - Latest complete quarter available for all industries: 2025-Q1
2025-07-11 16:03:17,842 - INFO - [process_and_score.py:110] - Recent 4 Quarters for analysis: ['2025-Q1', '2024-Q4', '2024-Q3', '2024-Q2']
2025-07-11 16:03:17,842 - INFO - [process_and_score.py:111] - Previous 4 Quarters for analysis: ['2024-Q1', '2023-Q4', '2023-Q3', '2023-Q2']
2025-07-11 16:03:17,881 - INFO - [process_and_score.py:131] - Calculated and normalized GDP size and growth scores.
2025-07-11 16:03:18,757 - INFO - [process_and_score.py:167] - Economic Value Add Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\economic_value_add_score.xlsx
2025-07-11 16:03:18,757 - INFO - [process_and_score.py:379] - --- Starting Rolling 12M Avg. Score Calculation for FRED Indicator: Industry_Production ---
2025-07-11 16:03:18,773 - INFO - [process_and_score.py:384] - Successfully loaded raw FRED data.
2025-07-11 16:03:18,789 - INFO - [process_and_score.py:398] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-11 16:03:18,789 - INFO - [process_and_score.py:399] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-11 16:03:18,822 - INFO - [process_and_score.py:432] - Calculated and normalized rolling average growth for 79 series.
2025-07-11 16:03:18,867 - INFO - [process_and_score.py:452] - Indutsrial Output Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\industrial_output_score.xlsx
2025-07-11 16:03:18,867 - INFO - [process_and_score.py:379] - --- Starting Rolling 12M Avg. Score Calculation for FRED Indicator: Capacity_Utilization ---
2025-07-11 16:03:18,885 - INFO - [process_and_score.py:384] - Successfully loaded raw FRED data.
2025-07-11 16:03:18,885 - INFO - [process_and_score.py:398] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-11 16:03:18,885 - INFO - [process_and_score.py:399] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-11 16:03:18,900 - INFO - [process_and_score.py:432] - Calculated and normalized rolling average growth for 26 series.
2025-07-11 16:03:18,947 - INFO - [process_and_score.py:466] - Capacity Utilization Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION - Copy\data\processed\capacity_utilization_score.xlsx
2025-07-11 16:03:18,947 - INFO - [process_and_score.py:701] - ===== Data Processing and Scoring Pipeline Finished =====
