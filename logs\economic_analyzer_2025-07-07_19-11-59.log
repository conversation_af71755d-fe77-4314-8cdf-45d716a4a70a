2025-07-07 19:11:59,112 - INFO - [logger.py:52] - <PERSON>gger initialized. Logging to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\logs\economic_analyzer_2025-07-07_19-11-59.log
2025-07-07 19:11:59,112 - INFO - [process_and_score.py:348] - ===== Starting Data Processing and Scoring Pipeline =====
2025-07-07 19:11:59,112 - INFO - [process_and_score.py:23] - --- Starting Economic Heft Score Calculation ---
2025-07-07 19:11:59,132 - INFO - [process_and_score.py:31] - Successfully loaded raw BEA GDP data and the master sheet.
2025-07-07 19:11:59,153 - INFO - [process_and_score.py:49] - Latest complete quarter available for all industries: 2025-Q1
2025-07-07 19:11:59,153 - INFO - [process_and_score.py:67] - Recent 4 Quarters for analysis: ['2025-Q1', '2024-Q4', '2024-Q3', '2024-Q2']
2025-07-07 19:11:59,153 - INFO - [process_and_score.py:68] - Previous 4 Quarters for analysis: ['2024-Q1', '2023-Q4', '2023-Q3', '2023-Q2']
2025-07-07 19:11:59,178 - INFO - [process_and_score.py:93] - Calculated and normalized GDP size and growth scores.
2025-07-07 19:11:59,194 - INFO - [process_and_score.py:129] - Economic Heft Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\economic_heft_score.csv
2025-07-07 19:11:59,194 - INFO - [process_and_score.py:147] - --- Starting Import Intensity Score Calculation ---
2025-07-07 19:11:59,234 - INFO - [process_and_score.py:153] - Successfully loaded raw DataWeb imports and exports data.
2025-07-07 19:11:59,238 - INFO - [process_and_score.py:159] - Aligning monthly trade data with latest GDP quarter: 2025-Q1
2025-07-07 19:11:59,247 - INFO - [process_and_score.py:171] - Analysis window covers 12 months ending on 2025-03-01.
2025-07-07 19:11:59,247 - INFO - [process_and_score.py:172] - First month in window: 2024-04-01, Last month in window: 2025-03-01.
2025-07-07 19:11:59,292 - INFO - [process_and_score.py:219] - Calculated and normalized import and deficit scores.
2025-07-07 19:11:59,313 - INFO - [process_and_score.py:244] - Import Intensity Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\import_intensity_score.csv
2025-07-07 19:11:59,313 - INFO - [process_and_score.py:260] - --- Starting Employment Score Calculation ---
2025-07-07 19:11:59,325 - INFO - [process_and_score.py:265] - Successfully loaded raw BLS employment data and the master sheet.
2025-07-07 19:11:59,325 - INFO - [process_and_score.py:271] - Aligning monthly employment data with latest GDP quarter: 2025-Q1
2025-07-07 19:11:59,341 - INFO - [process_and_score.py:282] - Recent 12-month window: 2024-04-01 to 2025-03-01
2025-07-07 19:11:59,341 - INFO - [process_and_score.py:283] - Previous 12-month window: 2023-04-01 to 2024-03-01
2025-07-07 19:11:59,375 - INFO - [process_and_score.py:325] - Calculated and normalized employment size and growth scores.
2025-07-07 19:11:59,387 - INFO - [process_and_score.py:340] - Employment Score calculation complete. Results saved to C:\Users\<USER>\Desktop\Autowiz\new_proj\sec_data\analytical\NAICS_FACTSET_SCORE_AUTOMATION\data\processed\employment_score.csv
2025-07-07 19:11:59,396 - INFO - [process_and_score.py:360] - ===== Data Processing and Scoring Pipeline Finished =====
