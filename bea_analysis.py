#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Thu Apr 24 23:55:30 2025

@author: kamal
"""

import pandas as pd

# Load the Excel file
df = pd.read_csv('bea_GDPbyIndustry_A.csv')  # Replace 'your_file.xlsx' with your actual file name


# Add an order column to preserve the original order
df['Order'] = df.groupby(['Industry', 'IndustrYDescription']).ngroup()


# Pivot the data so that years become columns
pivot_df = df.pivot(index=['Industry', 'IndustrYDescription', 'Order'], columns='Year', values='DataValue')


# Calculate growth rates
# Recent Growth: (2024 - 2023) / 2023
pivot_df['Recent_Growth_%'] = ((pivot_df[2024] - pivot_df[2023]) / pivot_df[2023]) * 100

# Two-Year Growth: (2024 - 2022) / 2022
pivot_df['Two_Year_Growth_%'] = ((pivot_df[2024] - pivot_df[2022]) / pivot_df[2022]) * 100

# Round growth rates to 1 decimal place
pivot_df['Recent_Growth_%'] = pivot_df['Recent_Growth_%'].round(1)
pivot_df['Two_Year_Growth_%'] = pivot_df['Two_Year_Growth_%'].round(1)

# Reset index for clean output
result_df = pivot_df.reset_index()

# Select relevant columns for output
result_df = result_df[['Industry', 'IndustrYDescription', 2022, 2023, 2024, 'Recent_Growth_%', 'Two_Year_Growth_%', 'Order']]

# Sort by original order
result_df = result_df.sort_values('Order').drop(columns='Order')

# Save to Excel or display
result_df.to_excel('growth_rates_output.xlsx', index=False)
print(result_df)


#underlying GDP

# Load the Excel file
df = pd.read_csv('bea_UnderlyingGDPbyIndustry_A.csv')  # Replace 'your_file.xlsx' with your actual file name


# Add an order column to preserve the original order
df['Order'] = df.groupby(['Industry', 'IndustrYDescription']).ngroup()


# Pivot the data so that years become columns
pivot_df = df.pivot(index=['Industry', 'IndustrYDescription', 'Order'], columns='Year', values='DataValue')


# Calculate growth rates
# Recent Growth: (2023 - 2022) / 2022
pivot_df['Recent_Growth_%'] = ((pivot_df[2023] - pivot_df[2022]) / pivot_df[2022]) * 100


# Round growth rates to 1 decimal place
pivot_df['Recent_Growth_%'] = pivot_df['Recent_Growth_%'].round(1)

# Reset index for clean output
result_df = pivot_df.reset_index()

# Select relevant columns for output
result_df = result_df[['Industry', 'IndustrYDescription', 2022, 2023, 'Recent_Growth_%',  'Order']]

# Sort by original order
result_df = result_df.sort_values('Order').drop(columns='Order')

# Save to Excel or display
result_df.to_excel('growth_rates_output_underlying.xlsx', index=False)
print(result_df)



#bea_GDPbyIndustry_components_A


df = pd.read_csv('bea_GDPbyIndustry_components_A.csv')  # Replace 'your_file.xlsx' with your actual file name


# Add an order column to preserve the original order
df['Order'] = df.groupby(['Industry', 'IndustrYDescription']).ngroup()


# Pivot the data so that years become columns
pivot_df = df.pivot(index=['Industry', 'IndustrYDescription', 'Order'], columns='Year', values='DataValue')


# Calculate growth rates
# Recent Growth: (2023 - 2022) / 2022
pivot_df['Recent_Growth_%'] = ((pivot_df[2023] - pivot_df[2022]) / pivot_df[2022]) * 100


# Round growth rates to 1 decimal place
pivot_df['Recent_Growth_%'] = pivot_df['Recent_Growth_%'].round(1)

# Reset index for clean output
result_df = pivot_df.reset_index()

# Select relevant columns for output
result_df = result_df[['Industry', 'IndustrYDescription', 2022, 2023, 'Recent_Growth_%',  'Order']]

# Sort by original order
result_df = result_df.sort_values('Order').drop(columns='Order')

# Save to Excel or display
result_df.to_excel('growth_rates_output_components.xlsx', index=False)
print(result_df)


#private fixed assets investments

df = pd.read_csv('bea_fa_industry.csv')  # Replace 'your_file.xlsx' with your actual file name


# Add an order column to preserve the original order
df['Order'] = df.groupby(['SeriesCode', 'LineDescription']).ngroup()


# Pivot the data so that years become columns
pivot_df = df.pivot(index=['SeriesCode', 'LineDescription', 'Order'], columns='TimePeriod', values='DataValue')


# Calculate growth rates
# Recent Growth: (2023 - 2022) / 2022
pivot_df['Recent_Growth_%'] = ((pivot_df[2023] - pivot_df[2022]) / pivot_df[2022]) * 100


# Round growth rates to 1 decimal place
pivot_df['Recent_Growth_%'] = pivot_df['Recent_Growth_%'].round(1)

# Reset index for clean output
result_df = pivot_df.reset_index()

# Select relevant columns for output
result_df = result_df[['SeriesCode', 'LineDescription', 2022, 2023, 'Recent_Growth_%',  'Order']]

# Sort by original order
result_df = result_df.sort_values('Order').drop(columns='Order')

# Save to Excel or display
result_df.to_excel('growth_rates_output_fixed_assets.xlsx', index=False)
print(result_df)
